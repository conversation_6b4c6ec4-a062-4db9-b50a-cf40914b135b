﻿using DarenApp.Models.Dic;
using DarenCore.Models;
using DarenCore.Services;
using DarenShare.BLL;

namespace DarenApp.BLL.Dic
{
    public static class DicExtension
    {
        public static void AddDicFactory(IServiceCollection services)
        {

            //员工
            services.AddKeyedSingleton<IDicFactory, DicFactory<EmployeeBLL>>(nameof(Employee));

            #region 产品
            
            //计量单位
            services.AddKeyedSingleton<IDicFactory, DicFactory<ProductUnitBLL>>(nameof(ProductUnit));
            
            #endregion

            //ERP软件名称
            services.AddKeyedSingleton<IDicFactory, DicFactory<ErpInfoBLL>>(nameof(ErpInfo));

            //部门类型
            services.AddKeyedSingleton<IDicFactory, DicFactory<ArrayDicBLL>>("departmentType");

            //验收类型
            services.AddKeyedSingleton<IDicFactory, DicFactory<ArrayDicBLL>>("AcceptType");

            //API类型
            services.AddKeyedSingleton<IDicFactory, DicFactory<ObjectDicBLL>>("ApiType");
        }
    }
}
