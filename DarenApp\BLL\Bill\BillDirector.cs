﻿using DarenCore.Common;

namespace DarenApp.BLL.Bill
{
    public class BillDirector
    {
        /// <summary>
        /// 制作单据
        /// </summary>
        /// <param name="builder"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        private static async Task<Result> MakeBillAsync(IBillBuilder builder, BillBuilderType type)
        {
            var r = await builder.BuildMainAsync(type);
            if (r.IsSuccess)
            {
                r = builder.BuildProduct(type);
                if (r.IsSuccess)
                {
                    r = builder.BuildSettlement(type);
                }
            }
            return r;
        }

        /// <summary>
        /// 保存单据
        /// </summary>
        /// <param name="builder"></param>
        /// <returns></returns>
        public static async Task<Result> SaveBillAsync(IBillBuilder builder)
        {
            var r = await MakeBillAsync(builder, BillBuilderType.SAVE);
            if (r.IsSuccess)
            {
                r = await builder.SaveBillAsync();
            }
            return r;
        }

        /// <summary>
        /// 审核单据
        /// </summary>
        /// <param name="builder"></param>
        /// <returns></returns>
        public static async Task<Result> AuditBillAsync(IBillBuilder builder)
        {
            var r = await MakeBillAsync(builder, BillBuilderType.AUDIT);
            if (r.IsSuccess)
            {
                r = await builder.AuditBillAsync();
            }
            return r;
        }

        /// <summary>
        /// 反审单据
        /// </summary>
        /// <param name="builder"></param>
        /// <returns></returns>
        public static Task<Result> CancelBillAsync(IBillBuilder builder)
        {
            return builder.CancelBillAsync();
        }

    }
}
