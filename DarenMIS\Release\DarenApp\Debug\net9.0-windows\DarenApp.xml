<?xml version="1.0"?>
<doc>
    <assembly>
        <name>DarenApp</name>
    </assembly>
    <members>
        <member name="M:DarenApp.BLL.Bill.BillBaseBuilder.CheckMainAsync(DarenCore.Common.BillBuilderType)">
            <summary>
            基本信息校验
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.BillBaseBuilder.BuildMainAsync(DarenCore.Common.BillBuilderType)">
            <summary>
            构建基本信息
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.BillBaseBuilder.GetPoductCount">
            <summary>
            获取商品行数
            </summary>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.BillBaseBuilder.CheckProduct(System.Int32,DarenCore.Common.BillBuilderType)">
            <summary>
            商品信息校验
            </summary>
            <param name="i"></param>
            <param name="type"></param>
            <returns>第一个参数值：0正常，1删除，2错误描述，3缺少属性</returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.BillBaseBuilder.InitProduct(System.Int32)">
            <summary>
            商品信息初始化
            </summary>
            <param name="i"></param>
        </member>
        <member name="M:DarenApp.BLL.Bill.BillBaseBuilder.CalculateProduct(System.Int32)">
            <summary>
            商品信息计算
            </summary>
            <param name="i"></param>
        </member>
        <member name="M:DarenApp.BLL.Bill.BillBaseBuilder.GetSettlementCount">
            <summary>
            获取结算信息行数
            </summary>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.BillBaseBuilder.CheckSettlement(System.Int32)">
            <summary>
            结算信息校验
            </summary>
            <param name="i"></param>
            <returns>第一个参数值：0正常，1删除，2错误描述，3缺少属性</returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.BillBaseBuilder.InitSettlement(System.Int32)">
            <summary>
            结算信息初始化
            </summary>
            <param name="i"></param>
        </member>
        <member name="M:DarenApp.BLL.Bill.BillBaseBuilder.CalculateSettlement(System.Int32)">
            <summary>
            结算信息计算
            </summary>
            <param name="i"></param>
        </member>
        <member name="M:DarenApp.BLL.Bill.BillBaseBuilder.BuildSettlement(DarenCore.Common.BillBuilderType)">
            <summary>
            构建结算信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.BillBaseBuilder.BuildSaveAsync(System.Boolean)">
            <summary>
            构建保存事件
            </summary>
            <param name="flag"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.BillBaseBuilder.BuildAuditAsync">
            <summary>
            构建审核事件
            </summary>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.BillBaseBuilder.BuildCancelAsync">
            <summary>
            构建反审事件
            </summary>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.BillBaseBuilder.SaveBillAsync">
            <summary>
            保存单据
            </summary>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.BillBaseBuilder.AuditBillAsync">
            <summary>
            审核单据
            </summary>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.BillBaseBuilder.CancelBillAsync">
            <summary>
            取消审核单据
            </summary>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.BillBaseBuilder.GetCurrentBalance(System.String)">
            <summary>
            获取往来单位往来余额
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.BillDirector.MakeBillAsync(DarenApp.BLL.Bill.IBillBuilder,DarenCore.Common.BillBuilderType)">
            <summary>
            制作单据
            </summary>
            <param name="builder"></param>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.BillDirector.SaveBillAsync(DarenApp.BLL.Bill.IBillBuilder)">
            <summary>
            保存单据
            </summary>
            <param name="builder"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.BillDirector.AuditBillAsync(DarenApp.BLL.Bill.IBillBuilder)">
            <summary>
            审核单据
            </summary>
            <param name="builder"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.BillDirector.CancelBillAsync(DarenApp.BLL.Bill.IBillBuilder)">
            <summary>
            反审单据
            </summary>
            <param name="builder"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.PurchaseIn.PurchaseInBuilder.CheckMainAsync(DarenCore.Common.BillBuilderType)">
            <summary>
            基本信息校验
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.PurchaseIn.PurchaseInBuilder.GetPoductCount">
            <summary>
            获取商品行数
            </summary>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.PurchaseIn.PurchaseInBuilder.CheckProduct(System.Int32,DarenCore.Common.BillBuilderType)">
            <summary>
            商品信息校验
            </summary>
            <param name="i"></param>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.PurchaseIn.PurchaseInBuilder.GetSettlementCount">
            <summary>
            获取结算信息行数
            </summary>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.PurchaseIn.PurchaseInBuilder.CheckSettlement(System.Int32)">
            <summary>
            /结算信息校验
            </summary>
            <param name="i"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.PurchaseIn.PurchaseInBuilder.InitSettlement(System.Int32)">
            <summary>
            结算信息初始化
            </summary>
            <param name="i"></param>
        </member>
        <member name="M:DarenApp.BLL.Bill.PurchaseIn.PurchaseInBuilder.BuildSaveAsync(System.Boolean)">
            <summary>
            保存
            </summary>
            <param name="flag">flag==true, 保存时调用;flag==false,审核时调用</param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.PurchaseIn.PurchaseInBuilder.BuildAuditAsync">
            <summary>
            审核
            </summary>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.PurchaseIn.PurchaseInBuilder.BuildCancelAsync">
            <summary>
            反审
            </summary>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.PurchaseIn.PurchaseInBuilder.UpdateStock(System.Int32)">
            <summary>
            更新库存和往来余额
            </summary>
            <param name="bill_status"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.PurchaseIn.PurchaseInService.GetBillForViewAsync(System.String)">
            <summary>
            根据ID获取单据 - 在视图中使用
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.PurchaseIn.PurchaseInService.GetBillByIdAsync(System.String)">
            <summary>
            根据ID获取单据
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.PurchaseIn.PurchaseInService.GetBillByOrderIdAsync(System.String)">
            <summary>
            根据订单号获取单据
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.PurchaseIn.PurchaseInService.MergeBillAsync(DarenApp.Models.Bill.PurchaseIn.PurchaseInMain)">
            <summary>
            合并单据
            </summary>
            <param name="main"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.SalesOut.SalesOutBuilder.CheckMainAsync(DarenCore.Common.BillBuilderType)">
            <summary>
            基本信息校验
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.SalesOut.SalesOutBuilder.GetPoductCount">
            <summary>
            获取商品行数
            </summary>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.SalesOut.SalesOutBuilder.CheckProduct(System.Int32,DarenCore.Common.BillBuilderType)">
            <summary>
            商品信息校验
            </summary>
            <param name="i"></param>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.SalesOut.SalesOutBuilder.GetSettlementCount">
            <summary>
            获取结算信息行数
            </summary>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.SalesOut.SalesOutBuilder.CheckSettlement(System.Int32)">
            <summary>
            /结算信息校验
            </summary>
            <param name="i"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.SalesOut.SalesOutBuilder.InitSettlement(System.Int32)">
            <summary>
            结算信息初始化
            </summary>
            <param name="i"></param>
        </member>
        <member name="M:DarenApp.BLL.Bill.SalesOut.SalesOutBuilder.BuildSaveAsync(System.Boolean)">
            <summary>
            保存
            </summary>
            <param name="flag">flag==true, 保存时调用;flag==false,审核时调用</param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.SalesOut.SalesOutBuilder.BuildAuditAsync">
            <summary>
            审核
            </summary>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.SalesOut.SalesOutBuilder.BuildCancelAsync">
            <summary>
            反审
            </summary>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.SalesOut.SalesOutBuilder.UpdateStock(System.Int32)">
            <summary>
            更新库存和往来余额
            </summary>
            <param name="bill_status"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.SalesOut.SalesOutService.GetBillForViewAsync(System.String)">
            <summary>
            根据ID获取单据 - 在视图中使用
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.SalesOut.SalesOutService.GetBillByIdAsync(System.String)">
            <summary>
            根据ID获取单据
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.SalesOut.SalesOutService.GetBillByOrderIdAsync(System.String)">
            <summary>
            根据订单号获取单据
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.SalesOut.SalesOutService.MergeBillAsync(DarenApp.Models.Bill.SalesOut.SalesOutMain)">
            <summary>
            合并单据
            </summary>
            <param name="main"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.SalesReturn.SalesReturnBuilder.CheckMainAsync(DarenCore.Common.BillBuilderType)">
            <summary>
            基本信息校验
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.SalesReturn.SalesReturnBuilder.GetPoductCount">
            <summary>
            获取商品行数
            </summary>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.SalesReturn.SalesReturnBuilder.CheckProduct(System.Int32,DarenCore.Common.BillBuilderType)">
            <summary>
            商品信息校验
            </summary>
            <param name="i"></param>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.SalesReturn.SalesReturnBuilder.GetSettlementCount">
            <summary>
            获取结算信息行数
            </summary>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.SalesReturn.SalesReturnBuilder.CheckSettlement(System.Int32)">
            <summary>
            /结算信息校验
            </summary>
            <param name="i"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.SalesReturn.SalesReturnBuilder.InitSettlement(System.Int32)">
            <summary>
            结算信息初始化
            </summary>
            <param name="i"></param>
        </member>
        <member name="M:DarenApp.BLL.Bill.SalesReturn.SalesReturnBuilder.BuildSaveAsync(System.Boolean)">
            <summary>
            保存
            </summary>
            <param name="flag">flag==true, 保存时调用;flag==false,审核时调用</param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.SalesReturn.SalesReturnBuilder.BuildAuditAsync">
            <summary>
            审核
            </summary>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.SalesReturn.SalesReturnBuilder.BuildCancelAsync">
            <summary>
            反审
            </summary>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.SalesReturn.SalesReturnBuilder.UpdateStock(System.Int32)">
            <summary>
            更新库存和往来余额
            </summary>
            <param name="bill_status"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.SalesReturn.SalesReturnService.GetBillForViewAsync(System.String)">
            <summary>
            根据ID获取单据 - 在视图中使用
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.SalesReturn.SalesReturnService.GetBillByIdAsync(System.String)">
            <summary>
            根据ID获取单据
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Bill.SalesReturn.SalesReturnService.MergeBillAsync(Climber.Model.SalesReturnMain)">
            <summary>
            合并单据
            </summary>
            <param name="main"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Dic.CustomerBLL.UpdateCurrentBalanceAsync(System.String,System.Decimal,System.String,System.Boolean)">
            <summary>
            更新往来余额
            </summary>
            <param name="id"></param>
            <param name="amount"></param>
            <param name="type"></param>
            <param name="isIncrease"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Dic.CustomerBLL.UpdateMemberBonusAsync(System.String,System.Decimal,System.Decimal,System.Decimal,System.Decimal,System.Boolean)">
            <summary>
            更新会员积分信息
            </summary>
            <param name="id"></param>
            <param name="receivable"></param>
            <param name="sum_bonus"></param>
            <param name="use_bonus"></param>
            <param name="use_recharge"></param>
            <param name="isIncrease"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Dic.CustomerBLL.UpdateMemberBonusAsync(DarenApp.Models.Dic.Customer)">
            <summary>
            更新会员积分
            </summary>
            <param name="m"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Dic.DepartmentBLL.GetListAsync(DarenApp.Models.Dic.Department,System.Int32)">
            <summary>
            获取列表数据
            </summary>
            <param name="m"></param>
            <param name="limitPower"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Dic.DepartmentBLL.GetPageJsonAsync(DarenApp.Models.Dic.Department,System.Int32,System.Int32,System.Int32)">
            <summary>
            获取分页数据
            </summary>
            <param name="m"></param>
            <param name="pageIndex"></param>
            <param name="pageSize"></param>
            <param name="limitPower"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Dic.SupplierBLL.UpdateCurrentBalanceAsync(System.String,System.Decimal,System.String,System.Boolean)">
            <summary>
            更新往来余额
            </summary>
            <param name="id"></param>
            <param name="amount"></param>
            <param name="type"></param>
            <param name="isIncrease"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Sys.TableService.CheckDicUseStatus1Aysnc(System.String,System.String,System.String)">
            <summary>
            检查数据字典使用状态(单值)
            </summary>
            <param name="column_name"></param>
            <param name="column_value"></param>
            <param name="display_name"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.BLL.Sys.TableService.CheckDicUseStatus2Aysnc(System.String,System.String,System.String)">
            <summary>
            检查数据字典使用状态(多值)
            </summary>
            <param name="column_name"></param>
            <param name="column_values"></param>
            <param name="display_name"></param>
            <returns></returns>
        </member>
        <member name="P:DarenApp.Controllers.BaseController.ctx">
            <summary>
            根据当前用户信息获取App应用上下文。
            </summary>
            <returns></returns>
        </member>
        <member name="M:DarenApp.Controllers.BaseController.GetContext">
            <summary>
            根据当前用户信息获取App应用上下文，创建数据库上下文。
            </summary>
            <returns></returns>
        </member>
        <member name="M:DarenApp.Controllers.BaseController.GetContext(System.String)">
            <summary>
            根据企业ID获取App应用上下文，创建数据库上下文。
            </summary>
            <param name="company_id"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.Controllers.Common.DicController.GetDic(System.String,System.String,System.String)">
            <summary>
            读取数据字典
            </summary>
            <param name="dicName">字典名称(与表名一致)</param>
            <param name="pid">父ID</param>
            <param name="orderby">排序方式</param>
            <returns></returns>
        </member>
        <member name="T:DarenApp.Controllers.Common.HomeController">
            <summary>
            入口(索引)控制器
            </summary>
        </member>
        <member name="M:DarenApp.Controllers.Common.HomeController.Login(System.String,System.String,System.String,System.String)">
            <summary>
            用户登录
            </summary>
            <param name="user_code">用户编码</param>
            <param name="password">用户密码</param>
            <param name="company_code">公司编码</param>
            <param name="platform_type">平台类型</param>
            <returns></returns>
        </member>
        <member name="T:DarenApp.Controllers.Dic.BaseDicController`2">
            <summary>
            控制器 - 数据字典
            </summary>
            <typeparam name="M">实体类</typeparam>
            <typeparam name="B">业务逻辑层BLL</typeparam>
        </member>
        <member name="M:DarenApp.Controllers.Dic.BaseDicController`2.GetList(`0)">
            <summary>
            获取列表
            </summary>
            <param name="m"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.Controllers.Dic.BaseDicController`2.GetPage(`0,System.Int32,System.Int32)">
            <summary>
            获取分页
            </summary>
            <param name="m"></param>
            <param name="pageIndex">页码</param>
            <param name="pageSize">每页行数</param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.Controllers.Dic.BaseDicController`2.Save(`0)">
            <summary>
            保存单条数据
            </summary>
            <param name="m"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.Controllers.Dic.BaseDicController`2.Saves(System.Collections.Generic.List{`0})">
            <summary>
            保存多条数据
            </summary>
            <param name="list"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.Controllers.Dic.BaseDicController`2.Delete(System.String)">
            <summary>
            删除单条数据
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.Controllers.Dic.BaseDicController`2.Deletes(System.String)">
            <summary>
            删除多条数据
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:DarenApp.Controllers.Dic.BaseDicController`2.UpdateIsUse(System.String,System.Int32)">
            <summary>
            更新使用状态
            </summary>
            <param name="id"></param>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="T:DarenApp.Controllers.Dic.ProductUnitController">
            <summary>
            控制器 - 产品计量单位
            </summary>
        </member>
    </members>
</doc>
