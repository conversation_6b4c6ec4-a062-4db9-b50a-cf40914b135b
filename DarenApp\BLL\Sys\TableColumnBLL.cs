﻿using DarenApp.DAL.Sys;
using DarenApp.Models.Sys;
using DarenCore.BLL.OldVersion;
using DarenCore.Interface;

namespace DarenApp.BLL.Sys
{
    public class TableColumnBLL(IWebAppContext ctx) : BaseBLL<TableColumn, TableColumnDAL>(ctx)
    {

        public async Task<(List<string>, Exception?)> GetTableNameListByColumnNameAsync(string name)
        {
            try
            {
                var list = await dal.GetTableNameListByColumnNameAsync(name);
                return (list, null);
            }
            catch (Exception ex)
            {
                return ([], ex);
            }
        }

    }
}
