﻿using DarenApp.Models.Dic;
using DarenCore.Common;
using DarenCore.DAL.OldVersion;
using DarenCore.DBUtils.SqlBuilder.Interfaces;
using System.Text;

namespace DarenApp.DAL.Dic.Common
{
    public class MemberLevelDAL : BaseDicDAL<MemberLevel>
    {
        public override string? GetTableName()
        {
            return "BM_HYJB";
        }

        public override Task<Result> SaveModelAsync(MemberLevel m, int _)
        {
            IInsertUpdateBuilder<MemberLevel> iu;

            if (m.is_new_row)
            {
                m.id = m.code;
                iu = CreateInsertBuilder();
                iu.AddItemStr("BM", m.code);
            }
            else
            {
                iu = CreateUpdateBuilder();
                iu.AddWhereItemString("BM", m.id);
            }
            iu.AddItemStr("MC", m.name);
            iu.AddItemStr("JP", m.spell);
            iu.AddItemStr("JG", m.use_price);
            iu.AddItemDecimal("SX", m.upper_limit);
            iu.AddItemDecimal("XX", m.lower_limit);
            iu.AddItemDecimal("ZK", m.use_discount);
            iu.AddItemDecimal("full_discount", m.full_discount);
            iu.AddItemStr("SB_FLAG", m.bonus_multiple > 1 ? "T" : "F");
            iu.AddItemStr("SJ_FLAG", m.is_automatic_upgrade == 1 ? "T" : "F");
            iu.AddItemBoolStr("QY_FLAG", m.is_use);

            return SaveModelAsync(iu, SaveDataType.DIC_CODE);
        }


        public Task<MemberLevel?> GetModel8ConsumptionAsync(decimal consumption)
        {
            return dao.GetModelByLimitAsync($"(SX>0 or XX>0) and SX<={consumption} and (XX>{consumption} or isnull(XX,0)=0)");
        }

        public override string GetLimit(MemberLevel m)
        {
            var sb = CreateStatementBuilder();
            sb.AddLikeBoth("BM", m.code);
            sb.AddLikeBoth("MC", m.name);
            if (!string.IsNullOrEmpty(m.search))
            {
                sb.AddLikeBoth("BM", m.search, null, "and (");
                sb.AddLikeBoth("MC", m.search, "or");
                sb.AddLikeBoth("JP", m.search, "or", null, ")");
            }
            sb.AddBoolStr("QY_FLAG", m.is_use);
            return sb.ToString();
        }

        public Task<List<MemberLevel>> GetList(MemberLevel m)
        {
            return dao.GetListByLimitAsync(GetLimit(m), "BM");
        }

        public override string GetTableFields()
        {
            return FieldsBuilder.fields;
        }

        private class FieldsBuilder
        {
            private static StringBuilder Build()
            {
                var sb = new StringBuilder();
                sb.Append("BM id,BM code,MC name,JP spell,");
                sb.Append("SX upper_limit,XX lower_limit,ZK use_discount,full_discount,JG use_price,");
                sb.Append("case when SB_FLAG='T' then 2 else 1 end bonus_multiple,");
                sb.Append("case when SJ_FLAG='T' then 1 else 0 end is_automatic_upgrade,");
                sb.Append("case when QY_FLAG='T' then 1 else 0 end is_use");
                return sb;
            }

            internal static readonly string fields = Build().ToString();
        }


    }
}
