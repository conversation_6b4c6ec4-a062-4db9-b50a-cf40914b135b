﻿using DarenApp.Models.Dic;
using DarenCore.Extension;

namespace DarenApp.DAL.Dic.Enterprisex.Customerx
{
    public abstract class BaseCustomerDAL : EnterpriseDAL<Customer>, ICustomerDAL
    {
        public override string GetLimit(Customer m)
        {
            var sb = CreateStatementBuilder();

            sb.AddLikeBoth("BM", m.code);
            sb.AddLikeBoth("MC", m.name);
            sb.AddLikeBoth("DZ", m.address);
            sb.AddLikeBoth("DH", m.telephone);
            sb.AddLikeBoth("SJ", m.mobile);
            sb.AddLikeBoth("JP", m.spell);

            if (!m.search.IsNullOrEmpty())
            {
                sb.AddLikeBoth("BM", m.search, null, "and (");
                sb.AddLikeBoth("MC", m.search, "or");
                sb.AddLikeBoth("DZ", m.search, "or");
                sb.AddLikeBoth("DH", m.search, "or");
                sb.AddLikeBoth("SJ", m.search, "or");
                sb.AddLikeBoth("JP", m.search, "or", null, ")");
            }

            if (m.type == -1)
            {
                sb.Add("and DWLX>1");
            }
            else
            {
                sb.Add("and DWLX=" + m.type);
            }
            sb.AddGroup("LB", m.category_id);
            sb.AddErpDepartmentAll("LLBM", m.department_id, user);
            sb.AddBoolStr("QY_FLAG", m.is_use);

            return sb.ToString();
        }

        //同步会员积分信息
        public Task<int> SyncMemberBonusAsync(Customer m)
        {
            var up = CreateUpdateBuilder();
            up.AddItemDecimal("LJJF", m.sum_bonus);
            up.AddItemDecimal("DXJF", m.use_bonus);
            up.AddItemDecimal("SYJF", m.remaining_bonus);
            up.AddItemDecimal("LJXF", m.sum_consumption);
            up.AddItemDecimal("CARD_XF", m.card_consumption);
            up.AddItemDecimal("CARD_YE", m.card_balance);
            up.AddItemDecimal("YUSYE", m.advance_balance);
            up.AddItemDecimal("WLYE", m.current_balance);
            up.AddItemStr("HYJB", m.member_level_id);
            up.AddItemStr("SYJG", m.use_price);
            up.AddItemDecimal("SYZK", m.use_discount);
            up.AddItemDateTime("ZJXFRQ", m.latest_date);
            up.AddWhereItemString("BM", m.id);
            return dao.UpdateAsync(up);
        }

        //更新会员级别
        public Task<int> UpdateMemberLevelAsync(MemberLevel m)
        {
            var up = CreateUpdateBuilder();
            up.AddItemDecimal("SYZK", m.use_discount);
            up.AddItemStr("SYJG", m.use_price);
            up.AddItemBoolStr("SB_FLAG", m.bonus_multiple);
            up.AddItemBoolStr("SJ_FLAG", m.is_automatic_upgrade);
            up.AddWhereItemString("HYJB", m.code);
            return dao.UpdateAsync(up);
        }
    }
}
