﻿using DarenApp.Models.Dic;
using DarenCore.DAL.OldVersion;
using DarenCore.Extension;

namespace DarenApp.DAL.Dic.Warehousex
{
    public abstract class BaseWarehouseDAL : BaseDicDAL<Warehouse>, IWarehouseDAL
    {
        public override string? GetTableName()
        {
            return "BM_CK";
        }

        public async Task<Warehouse> GetFirstAsync(string department_id)
        {
            var wb = CreateWhereBuilder();
            wb.AddItemString("BMBM", department_id);
            wb.AddItemBoolString("QY_FLAG", 1);
            var m = await dao.GetModelByWhereBuilderAsync(wb, "BM");
            if (m == null)
            {
                var id = department_id + "01";
                m = new Warehouse() { id = id, code = id, name = "默认仓库" };
            }
            return m;
        }

        public override string GetLimit(Warehouse m)
        {
            var sb = CreateStatementBuilder();
            sb.AddStr("BM", m.code);
            sb.AddStr("MC", m.name);
            if (!m.search.IsNullOrEmpty())
            {
                sb.AddLikeBoth("BM", m.search, null, "and (");
                sb.AddLikeBoth("MC", m.search, "or");
                sb.AddLikeBoth("JP", m.search, "or", null, ")");
            }
            sb.AddDic("BMBM", m.department_id);
            sb.AddBoolStr("QY_FLAG", m.is_use);
            return sb.ToString();
        }
    }
}
