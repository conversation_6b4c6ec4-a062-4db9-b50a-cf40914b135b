﻿using Climber.Model;
using DarenApp.Models.Bill.SalesReturn;
using DarenCore.Common;

namespace DarenApp.BLL.Bill.SalesReturn.ProductChecker
{
    public class DrugChecker : IBillProductChecker<SalesReturnMain, SalesReturnProduct>
    {

        public (int, string?, string?) Check(SalesReturnMain main, List<SalesReturnProduct> list, int i)
        {
            var p = list[i];

            //批号校验
            if (p.cost_mode == "A")
            {
                if (string.IsNullOrEmpty(p.batch_no))
                {
                    return (3, null, "生产批号");
                }
            }
            else
            {
                p.batch_no = "";
            }

            //效期校验
            if (ErpDataRequire.IsRequireExpirtyDate(p.expires_in))
            {
                if (p.expires_date == null)
                {
                    return (3, null, "有效期至");
                }
                else if (p.expires_date < main.bill_date)
                {
                    return (2, "商品已过期！", "");
                }
            }
            else
            {
                p.expires_date = null;
            }

            //仓库校验
            if (p.cost_mode != "N" && string.IsNullOrEmpty(p.warehouse_id))
            {
                return (3, null, "仓库");
            }

            return (0, null, null);
        }

    }
}
