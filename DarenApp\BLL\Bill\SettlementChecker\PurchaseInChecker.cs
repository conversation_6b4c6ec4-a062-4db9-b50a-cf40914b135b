﻿using DarenApp.Models.Bill.PurchaseIn;

namespace DarenApp.BLL.Bill.SettlementChecker
{
    public class PurchaseInChecker
    {
        public (int, string?, string?) Check(List<PurchaseInSettlement> list, int i)
        {
            var s = list[i];

            //删除空行
            if (s == null || string.IsNullOrEmpty(s.settlement_id))
            {
                list.RemoveAt(i);
                return (1, null, null);
            }

            return (0, null, null);
        }

    }
}
