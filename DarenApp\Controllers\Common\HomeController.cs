﻿using DarenApp.Services;
using Microsoft.AspNetCore.Mvc;

namespace DarenApp.Controllers.Common
{
    //IActionResult  JsonResult  ViewResult  RedirectResult

    /// <summary>
    /// 入口(索引)控制器
    /// </summary>

    //[Route("app/AppHome/[action]")]
    public class HomeController : BaseController
    {
        //public async Task<JsonResult> Index()
        //{
        //    using var daren_app_ctx = WebAppContext.CreateAppContext_UseBook(HttpContext, true, "2020101517155619722736600026");
        //    if (daren_app_ctx.Exception != null)
        //    {
        //        return new JsonResult(daren_app_ctx.Exception);
        //    }
        //    var (data, _) = await new DicProductBLL(daren_app_ctx).GetPageJArrayForWwz(1, 2);

        //    return new JsonResult(data);
        //}


        /// <summary>
        /// 用户登录
        /// </summary>
        /// <param name="user_code">用户编码</param>
        /// <param name="password">用户密码</param>
        /// <param name="company_code">公司编码</param>
        /// <param name="platform_type">平台类型</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> Login(string user_code, string password, string company_code, string platform_type)
        {
            var r = await OnlineUserService.Instance.Login(HttpContext, user_code, password, company_code);// new OnlineUserBLL().Login(user);
            return new JsonResult(r);
        }

        //[ApiExplorerSettings(IgnoreApi =true)]  --隐藏接口

        //安全退出 
        public async Task<JsonResult> Logout()
        {
            var r = await OnlineUserService.Instance.Logout(HttpContext, ctx.User.id);
            return new JsonResult(r);
        }


        public ActionResult Error(int? code)
        {
            ViewData["title"] = "错误提示";
            if (code == 404)
            {
                return View("Error404");
            }
            else if (code == 500)
            {
                return View("Error500");
            }
            return View();
        }
    }
}