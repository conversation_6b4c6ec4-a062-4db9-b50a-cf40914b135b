﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace DarenCore.Models.BaseModel
{
    public class BaseDateModel : BaseIdModel
    {
        public BaseDateModel()
        {

        }

        [DisplayName("添加日期")]
        public DateTime? add_date { get; set; }

        [DisplayName("修改日期")]
        public DateTime? edit_date { get; set; }

        [DisplayName("操作员")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string? operator_id { get; set; }

        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string? operator_name { get; set; }
    }
}