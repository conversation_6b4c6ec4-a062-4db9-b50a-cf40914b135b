is_global = true
build_property.TargetFramework = net9.0-windows
build_property.TargetPlatformMinVersion = 7.0
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = DarenApp
build_property.RootNamespace = DarenApp
build_property.ProjectDir = E:\cursortest\dear\DarenApp\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.CsWinRTUseWindowsUIXamlProjections = false
build_property.RazorLangVersion = 9.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = E:\cursortest\dear\DarenApp
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[E:/cursortest/dear/DarenApp/Views/AppHome/Error.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQXBwSG9tZVxFcnJvci5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[E:/cursortest/dear/DarenApp/Views/AppHome/Error404.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQXBwSG9tZVxFcnJvcjQwNC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[E:/cursortest/dear/DarenApp/Views/AppHome/Error500.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQXBwSG9tZVxFcnJvcjUwMC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 
