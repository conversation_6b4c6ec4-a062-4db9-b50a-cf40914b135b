﻿using DarenApp.Models.Stock;
using DarenCore.Common;
using DarenCore.DBUtils.SqlBuilder.Interfaces;
using DarenCore.Extension;
using DarenCore.Models;
using System.Text;

namespace DarenApp.DAL.Stock.ProductStockDAL
{
    public class DrugProductStockDAL : BaseProductStockDAL
    {

        public override async Task<Result> SaveModelAsync(ProductStock m, int _)
        {
            IInsertUpdateBuilder<ProductStock> iu;
            var exist = await ExistsModelAsync(m);
            if (!exist)
            {
                iu = CreateInsertBuilder();
                iu.AddItemStr("BMBM", m.department_id);
                iu.AddItemStr("CKBM", m.warehouse_id);
                iu.AddItemStr("YPBM", m.product_id);
                iu.AddItemStr("SCPH", m.batch_no);
            }
            else
            {
                iu = CreateUpdateBuilder();
                iu.AddWhereItemString("BMBM", m.department_id);
                iu.AddWhereItemString("CKBM", m.warehouse_id);
                iu.AddWhereItemString("YPBM", m.product_id);
                iu.AddWhereItemString("SCPH", m.batch_no);
            }

            iu.AddItemDecimal("KCSL", m.quantity);
            iu.AddItemDecimal("KYSL", m.available);
            iu.AddItemDecimal("CBJJ", m.cost_price);
            iu.AddItemDecimal("KCJE", m.amount);

            iu.AddItemInt("YXQX", m.expires_in);
            iu.AddItemDateTime("RKRQ", m.store_date);
            iu.AddItemDateTime("SCRQ", m.manufacture_date);
            iu.AddItemDateTime("YXQZ", m.expires_date);

            iu.AddItemStr("CKMC", m.warehouse_name);
            iu.AddItemStr("DWBM", m.supplier_id);
            iu.AddItemStr("DWMC", m.supplier_name);
            iu.AddItemStr("HWBH", m.location_no);
            iu.AddItemStr("BZ", m.note);

            return await SaveModelAsync(iu, SaveDataType.DEFAULT);
        }

        public override Task<bool> ExistsModelAsync(ProductStock m)
        {
            var wb = CreateWhereBuilder();
            wb.AddItemString("BMBM", m.department_id);
            wb.AddItemString("CKBM", m.warehouse_id);
            wb.AddItemString("YPBM", m.product_id);
            wb.AddItemString("SCPH", m.batch_no);
            return dao.ExistsByWhereBuilderAsync(wb);
        }

        public override Task<ProductStock?> GetModelAsync(ProductStock m)
        {
            var wb = CreateWhereBuilder();
            wb.AddItemString("BMBM", m.department_id);
            wb.AddItemString("CKBM", m.warehouse_id);
            wb.AddItemString("YPBM", m.product_id);
            wb.AddItemString("SCPH", m.batch_no);
            return dao.GetModelByWhereBuilderAsync(wb);
        }

        public override async Task<Result> UpdateStockAsync(ProductStock m, int sn, bool isIncrease, bool isAudit, ParamOption option)
        {
            decimal quantity = 0, available = 0, cost_price = 0, amount = 0;
            IInsertUpdateBuilder<ProductStock> iu;
            var e = await GetModelAsync(m);
            if (e == null)
            {
                iu = CreateInsertBuilder();
                iu.AddItemStr("BMBM", m.department_id);
                iu.AddItemStr("CKBM", m.warehouse_id);
                iu.AddItemStr("YPBM", m.product_id);
                iu.AddItemStr("SCPH", m.batch_no);
            }
            else
            {
                iu = CreateUpdateBuilder();
                iu.AddWhereItemString("BMBM", m.department_id);
                iu.AddWhereItemString("CKBM", m.warehouse_id);
                iu.AddWhereItemString("YPBM", m.product_id);
                iu.AddWhereItemString("SCPH", m.batch_no);
                quantity = e.quantity;
                available = e.available;
                amount = e.amount;
            }
            if (isIncrease)
            {
                quantity += m.quantity;
                available += m.quantity;
                amount += m.amount;
            }
            else
            {
                quantity -= m.quantity;
                available -= m.quantity;
                amount -= m.amount;
            }
            if (quantity > 0)
            {
                cost_price = (amount / quantity).Fix(option.DigitsSetting.jj);
            }
            else if (quantity < 0 && option.ParamConfig.is_negative_stock == 0)
            {
                return Result.Fail((isAudit ? "审核成功" : "取消审核") + "后，第" + sn + "行商品的库存数量将为负数");
            }
            iu.AddItemDecimal("KCSL", quantity);
            iu.AddItemDecimal("KYSL", available);
            iu.AddItemDecimal("CBJJ", cost_price);
            iu.AddItemDecimal("KCJE", amount);

            iu.AddItemInt("YXQX", m.expires_in);
            iu.AddItemDateTime("RKRQ", m.store_date);
            iu.AddItemDateTime("SCRQ", m.manufacture_date);
            iu.AddItemDateTime("YXQZ", m.expires_date);

            iu.AddItemStr("CKMC", m.warehouse_name);
            iu.AddItemStr("DWBM", m.supplier_id);
            iu.AddItemStr("DWMC", m.supplier_name);
            iu.AddItemStr("HWBH", m.location_no);
            iu.AddItemStr("BZ", m.note);

            return await SaveModelAsync(iu, SaveDataType.DEFAULT);
        }

        //public override List<ProductStock> GetList(string department_id, string warehouse_id, string product_id)
        //{
        //    var sb = new StatementBuilder();
        //    sb.AddStr("BMBM", department_id);
        //    sb.AddStr("CKBM", warehouse_id);
        //    sb.AddStr("YPBM", product_id);
        //    sb.Add("and KYSL>0");
        //    return dao.GetList8Limit<ProductStock>(sb.ToString(), "YXQZ,SCPH");
        //}

        //public override List<ProductStock> GetListForBatchNoChoice(string department_id, string warehouse_id, string product_id, string batch_no)
        //{
        //    var sb = new StatementBuilder();
        //    sb.AddStr("BMBM", department_id);
        //    sb.AddStr("CKBM", warehouse_id);
        //    sb.AddStr("YPBM", product_id);
        //    sb.AddStr("SCPH", batch_no);
        //    sb.Add("and KYSL>0");
        //    return dao.GetList8Limit<ProductStock>(sb.ToString(), "YXQZ,SCPH");
        //}

        public override string GetTableFields()
        {
            return FieldsBuilder.fields;
        }

        private class FieldsBuilder
        {
            private static StringBuilder Build()
            {
                var sb = new StringBuilder();
                sb.Append("BMBM department_id,CKBM warehouse_id,CKMC warehouse_name,");
                sb.Append("YPBM product_id,SCPH batch_no,SCRQ manufacture_date,YXQX expires_in,YXQZ expires_date,");
                sb.Append("KYSL available,KCSL quantity,CBJJ cost_price,KCJE amount,HWBH location_no,BZ note");
                return sb;
            }

            internal static readonly string fields = Build().ToString();
        }
    }
}
