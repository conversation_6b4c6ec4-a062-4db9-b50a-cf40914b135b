﻿using DarenApp.Models.Dic;
using DarenCore.DAL.OldVersion;

namespace DarenApp.DAL.Dic.Employeex
{
    public abstract class BaseEmployeeDAL : BaseDicDAL<Employee>, IEmployeeDAL
    {
        public override string? GetTableName()
        {
            return "BM_YG";
        }

        public Task<Employee?> GetModelForLoginAsync(string code)
        {
            var pb = CreateParamBuilder();
            pb.AddString("BM", code);
            return dao.GetModelByLimitAsync("BM=@BM or DLMC=@BM", null, null, pb.GetParams());
        }

        public override string GetLimit(Employee m)
        {
            var sb = CreateStatementBuilder();
            sb.AddLikeBoth("BM", m.code);
            sb.AddLikeBoth("MC", m.name);
            sb.AddLikeBoth("DLMC", m.login_name);
            if (!string.IsNullOrEmpty(m.search))
            {
                sb.AddLikeBoth("BM", m.search, null, "and (");
                sb.AddLikeBoth("MC", m.search, "or");
                sb.AddLikeBoth("DLMC", m.search, "or", null, ")");
            }
            sb.AddDic("BMBM", m.department_id);
            sb.AddBoolStr("QY_FLAG", m.is_use);
            return sb.ToString();
        }

        //public Task<string> GetPageAsync(Employee m)
        //{
        //    return dao.GetPageJson(GetLimit(m), "BM", (m.pageIndex - 1) * m.pageSize, m.pageSize);
        //}

        public Task<int> UpdatePasswordAsync(string id, string password)
        {
            var up = CreateUpdateBuilder();
            up.AddItemStr("MM", password);
            up.AddWhereItemString("BM", id);
            return dao.UpdateAsync(up);
        }

    }
}
