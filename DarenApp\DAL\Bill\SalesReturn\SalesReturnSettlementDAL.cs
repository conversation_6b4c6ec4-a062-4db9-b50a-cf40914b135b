using DarenApp.Models.Bill.SalesReturn;
using DarenCore.Common;
using DarenCore.DAL.OldVersion;
using DarenCore.DBUtils.SqlBuilder.Interfaces;
using System.Text;

namespace DarenApp.DAL.Bill.SalesReturn
{
    public class SalesReturnSettlementDAL : BaseDAL<SalesReturnSettlement>
    {

        public override string? GetTableName()
        {
            return "XTD_JS";
        }

        public override Task<Result> SaveModelAsync(SalesReturnSettlement m, int _)
        {
            IInsertUpdateBuilder<SalesReturnSettlement> iu;

            if (m.is_new_row)
            {
                iu = CreateInsertBuilder();
                iu.AddItemStr("ID", m.id);
                iu.AddItemInt("XH", m.sn);
            }
            else
            {
                iu = CreateUpdateBuilder();
                iu.AddWhereItemString("ID", m.id);
                iu.AddWhereItemInt("XH", m.sn);
            }

            iu.AddItemStr("BM", m.settlement_id);
            iu.AddItemStr("MC", m.settlement_name);
            iu.AddItemStr("KM1BM", m.subject1_id);
            iu.AddItemStr("KM2BM", m.subject2_id);
            iu.AddItemStr("KM3BM", m.subject3_id);
            iu.AddItemStr("KMBM", m.subject_id);
            iu.AddItemStr("KMQC", m.subject_name);
            iu.AddItemDecimal("JSJE", m.settlement_amount);
            iu.AddItemDecimal("DXJF", m.use_bonus);
            iu.AddItemStr("PH", m.doc_no);
            iu.AddItemInt("LX", m.settlement_type);

            return SaveModelAsync(iu, SaveDataType.DEFAULT);
        }


        public override string GetTableFields()
        {
            return FieldsBuilder.fields;
        }

        private class FieldsBuilder
        {
            private static StringBuilder Build()
            {
                var sb = new StringBuilder();
                sb.Append("ID id,XH sn,ID pid,BM settlement_id,MC settlement_name,");
                sb.Append("KMBM subject_id,KMQC subject_name, KM1BM subject1_id,KM2BM subject2_id, KM3BM subject3_id,");
                sb.Append("JSJE settlement_amount,DXJF use_bonus,PH doc_no");
                return sb;
            }

            internal static readonly string fields = Build().ToString();
        }

    }

}
