﻿namespace DarenCore.Models
{

    //系统数值精度配置

    public class DigitsSetting
    {
        public DigitsSetting()
        {
            sl = 2;
            jj = 2;
            sj = 2;
            je = 2;
            jd = 2;
            jf = 0;
            zk = 0;
            fy = 2;
            tr = 3;
        }

        public int sl { get; set; }//数量
        public int jj { get; set; }//进价
        public int sj { get; set; }//售价
        public int je { get; set; }//普通金额(销售金额除外,如进货金额，盘点金额，库存金额，优惠金额, 销售成本，销售毛利等）
        public int jd { get; set; }//销售金额
        public int jf { get; set; }//积分
        public int zk { get; set; }//折扣
        public int fy { get; set; }//费用(邮费）
        public int tr { get; set; }//税率

    }

}