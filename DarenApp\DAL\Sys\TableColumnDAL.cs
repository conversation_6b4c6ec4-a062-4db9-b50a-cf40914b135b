﻿using DarenApp.Models.Sys;
using DarenCore.DAL.OldVersion;
using System.Text;

namespace DarenApp.DAL.Sys
{
    public class TableColumnDAL : BaseDAL<TableColumn>
    {
        public override string? GetTableName()
        {
            return "GG_TB_MX";
        }

        public Task<List<string>> GetTableNameListByColumnNameAsync(string name)
        {
            return dao.DbHelper.GetListDbAsync<string>($"select distinct TableName from {GetTableName()} where MC='{name}'");
        }

        public override string GetTableFields()
        {
            return FieldsBuilder.fields;
        }

        private class FieldsBuilder
        {
            private static StringBuilder Build()
            {
                var sb = new StringBuilder();
                sb.Append("TableName ID,XH sn,TableName pid, MC name,LX type,KD length,XSW digits,");
                sb.Append("case when AllowNull='T' then 1 else 0 end is_allow_null,");
                sb.Append("case when AllowNull='T' then 1 else 0 end is_primary_key,");
                sb.Append("MR default_value,SM display_name");
                return sb;
            }

            internal static readonly string fields = Build().ToString();
        }
    }
}