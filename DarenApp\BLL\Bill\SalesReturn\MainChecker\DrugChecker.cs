﻿using Climber.Model;
using DarenCore.Common;

namespace DarenApp.BLL.Bill.SalesReturn.MainChecker
{
    public class DrugChecker : IBillMainChecker<SalesReturnMain>
    {
        public bool Check(SalesReturnMain m, out string? msg)
        {
            if (!ErpDataCheck.IsBillDate(m.bill_date, out msg)) return false;
            if (!ErpDataCheck.IsCustomerSelected(m.enterprise_id, out msg)) return false;
            if (!ErpDataCheck.IsDepartmentSelected(m.department_id, out msg)) return false;
            if (!ErpDataCheck.IsHandlerSelected(m.handler_id, out msg)) return false;
            if (!ErpDataCheck.IsSettlementSelected(m.settlement_id, out msg)) return false;
            if (!ErpDataCheck.IsPayGreaterThenPayable(m.balance, out msg)) return false;
            return true;
        }
    }
}
