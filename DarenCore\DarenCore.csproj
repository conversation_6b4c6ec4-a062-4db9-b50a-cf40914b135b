﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0-windows</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <OutputType>Library</OutputType>
    <BaseOutputPath>E:\cursortest\dear\DarenMIS\Release\DarenCore</BaseOutputPath>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <WarningLevel>9999</WarningLevel>
    <GenerateAssemblyInfo>True</GenerateAssemblyInfo>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <WarningLevel>9999</WarningLevel>
    <GenerateAssemblyInfo>True</GenerateAssemblyInfo>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.0" />
    <PackageReference Include="NLog" Version="5.3.4" />
    <PackageReference Include="NPinyin.Core" Version="3.0.0" />
    <PackageReference Include="SharpZipLib" Version="1.4.2" />
    <PackageReference Include="System.Drawing.Common" Version="9.0.1" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Newtonsoft.Json">
      <HintPath>..\Packages\Newtonsoft.Json.6.0.4\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="ThoughtWorks.QRCode">
      <HintPath>..\Library\ThoughtWorks.QRCode.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>
