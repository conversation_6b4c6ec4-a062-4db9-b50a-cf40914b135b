﻿using DarenApp.BLL.Dic;
using DarenApp.DAL;
using DarenApp.Services;
using DarenCore.Abstract;
using DarenCore.Common;
using DarenCore.Helper;
using DarenCore.Interface;
using DarenCore.Services;
using DarenShare.DbContext;
using DarenShare.Services;
using System.Reflection;

namespace DarenApp.Common
{
    public class WebApp(AppOptions options) : AbstractWebApp
    {
        private readonly AppOptions _options = options;

        public override void InitService()
        {
            //初始化JWT服务
            JwtHelper.Init(_options.Config.JwtConfig);

            //初始化缓存服务
            CacheService.Init(_options.Config.CacheExpirationMinute);

            //初始化字典服务
            DicService.Init(CacheService.Instance);

            //初始化账套服务
            BookService.Init(_options.Config.CleanBookConfigCacheTime);

            //初始化系统参数服务
            ParamOptionService.Init(_options.Config.CleanParamConfigCacheIntervalMinutes);

            //初始化在线用户服务
            OnlineUserService.Init(
                _options.Config.CleanOnlineUserIntervalMinutes,
                _options.Config.JwtConfig.ExpiresMinutes
            );
        }

        public override AbstractWebAppConfig InitConfig()
        {
            var config = new AbstractWebAppConfig()
            {
                AppName = _options.Config.AppName,
                IsDevelopment = _options.Config.IsDevelopment == 1,
                Urls = _options.Config.AppUrls,
                ErrorPage = _options.Setting.ErrorPage,
                JwtHelper = JwtHelper.Instance,
                OnStopped = () =>
                {
                    CacheHelper.Dispose();
                    BookService.Instance.DbConfigCache.Dispose();
                    ParamOptionService.Instance.ParamOptionCache.Dispose();
                    OnlineUserService.Instance.OnlineUserCache.Dispose();
                }
            };

            return config;
        }

        public override void OnBuilder(WebApplicationBuilder builder)
        {
            builder.Configuration.AddConfiguration(_options.ConfigManager.root);
            //builder.WebHost.UseConfiguration(options.ConfigManager.root);
            //services.Configure<IConfiguration>(options.ConfigManager.root);
            //以上三种，结果是一样的，都带有系统默认的参数
            //下面这一种，只有用户自己的参数
            //services.AddSingleton<IConfiguration>(options.ConfigManager.root);
        }

        public override void OnAddServices(IServiceCollection services)
        {
            //var options = new ServiceOptions();
            //services.AddOptions<ServiceOptions>("key");
            //services.ConfigureOptions(options);

            services.Configure<ServiceOptions>((options) =>
            {
                options.OnlineUserExpiresMinutes = _options.Config.JwtConfig.ExpiresMinutes;
                options.CleanOnlineUserIntervalMinutes = _options.Config.CleanOnlineUserIntervalMinutes;
            });

            base.OnAddServices(services);

            //添加全局选项实例
            services.AddSingleton(_options);

            //添加全局APP参数配置实例
            services.AddSingleton<IParamOptionService>(ParamOptionService.Instance);

            //添加账套服务实例
            services.AddSingleton<IBookService>(BookService.Instance);

            //注入用户数据库工厂服务
            DbContextFactoryExtension.AddDbContextFactory(services, _options.Config.DbConfig);

            //注入字典工厂服务
            DicExtension.AddDicFactory(services);

            //注入数据访问层(DAL)工厂服务
            DALFactoryExtension.AddDalFactory(services);

            //注入后台任务 - 清理在线用户信息
            services.AddHostedService<OnlineUserCleanService<WebAppContext>>();

            //注册Swagger
            services.AddSwaggerGen(u =>
            {
                u.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo
                {
                    Version = "Ver2024.12.24",
                    Title = "客来宝API",
                    Description = "湖北大任软件技术有限公司出品的企业一体化管理信息系统接口",
                    Contact = new Microsoft.OpenApi.Models.OpenApiContact
                    {
                        Name = "一生何求",
                        Email = "<EMAIL>"
                    }
                });
                #region 读取xml信息
                //查看地址：http://localhost/swagger/index.html
                //在页面中显示注释信息的办法：
                //1.在Visual Studio中右键点击项目，选择“属性”。在“生成”选项卡中，勾选“输出”部分的“XML文档文件”复选框。
                //2.按下面方法配置注释文件生成的位置
                // 使用反射获取xml文件，并构造出文件的路径
                var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
                var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
                // 启用xml注释，该方法第二个参数启用控制器的注释，默认为false.
                u.IncludeXmlComments(xmlPath, true);
                #endregion
            });
        }

        public override void OnUseMiddleware(WebApplication app)
        {
            base.OnUseMiddleware(app);

            //app.MapControllerRoute(
            //    name: "area",
            //    pattern: "{area}/{controller}/{action}/{id?}"
            // );

            app.MapControllerRoute(
               name: "default",
               pattern: "{controller=home}/{action=index}/{id?}"
             );

            //启用Swagger中间件
            app.UseSwagger();

            //配置SwaggerUI
            app.UseSwaggerUI(u =>
            {
                u.SwaggerEndpoint("/swagger/v1/swagger.json", "WebAPI_v1");
            });
        }
    }
}
