﻿using DarenCore.Common;
using DarenCore.DBUtils.SqlBuilder.Interfaces;
using DarenCore.Models;
using System.Text;

namespace DarenApp.DAL.Sys.ParamConfigDAL
{
    public class CommonParamConfigDAL : BaseParamConfigDAL
    {
        public override Task<Result> SaveModelAsync(ParamConfig m)
        {
            IInsertUpdateBuilder<ParamConfig> iu;
            if (string.IsNullOrEmpty(m.id))
            {
                iu = CreateInsertBuilder();
            }
            else
            {
                iu = CreateUpdateBuilder();
                iu.AddWhereItemString(x => x.company_name, m.id);
            }
            iu.AddItemString(x => x.company_name, m.company_name);
            iu.AddItemString(x => x.company_short, m.company_short);
            iu.AddItemString(x => x.admin_password, m.admin_password);
            return SaveModelAsync(iu, SaveDataType.PRIMARY);
        }

        public override Task<string?> GetNoticeAsync()
        {
            var sb = base.CreateSelectBuilder();
            sb.AddField(x => x.notice);
            return dao.GetStringBySelectBuilderAsync(sb);
        }

        public override string GetTableFields()
        {
            return FieldsBuilder.fields;
        }

        private class FieldsBuilder
        {
            private static StringBuilder Build()
            {
                var sb = new StringBuilder();
                sb.Append("DWMC id,DWMC company_name,DWJC company_short,AdminPassword admin_password,");
                sb.Append("DEFAULT_GHS default_supplier_id,DEFAULT_KH default_customer_id,");
                sb.Append("JJ_GZFS purchase_price_mode,SJ_GZFS sales_price_mode,");
                sb.Append("RT tax_rate,JF bonus_rate,DX bonus2cash_rate,");
                sb.Append("QCDZ_BL fd_discount,QCDZ_JFBS fd_bonus_multiple,MSG notice,");
                sb.Append("case when QCDZ_QY='T' then 1 else 0 end fd_is_enable,");
                sb.Append("case when QCDZ_VIP='T' then 1 else 0 end fd_is_member_only,");
                sb.Append("case when LIMIT_YG='T' then 1 else 0 end is_limit_employee,");
                sb.Append("case when ALLOW_FKC='T' then 1 else 0 end is_negative_stock");
                return sb;
            }

            internal static readonly string fields = Build().ToString();
        }
    }
}