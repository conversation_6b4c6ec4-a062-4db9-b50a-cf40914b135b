﻿using DarenApp.DAL.Dic.Common;
using DarenApp.Models.Dic;
using DarenCore.BLL;
using DarenCore.BLL.OldVersion;
using DarenCore.Common;
using DarenCore.Interface;

namespace DarenApp.BLL.Dic
{
    public class MemberLevelBLL : BaseDicBLL<MemberLevel, MemberLevelDAL>, IControllerDicBLL<MemberLevel>
    {
        public MemberLevelBLL()
        {

        }

        public MemberLevelBLL(IWebAppContext ctx) : base(ctx)
        {

        }

        public override async Task<(string key, string id, object? data, Exception?)> SaveModelAsync(MemberLevel m, int sn)
        {
            if (string.IsNullOrEmpty(m.spell))
            {
                m.spell = GetFirstSpell(m.name);
            }

            try
            {
                await dal.SaveModelAsync(m, sn);
                return (m.key, m.id, m.spell, null);
            }
            catch (Exception ex)
            {
                return ("", "", null, ex);
            }
        }

        public Task<Result> SaveModelAsync(MemberLevel m)
        {
            return base.SaveModelAsync(m, true);
        }

        public Task<Result> SaveModelsAsync(List<MemberLevel> list)
        {
            return base.SaveModelsAsync(list, true);
        }

        public async Task<(MemberLevel?, Exception?)> GetModel8ConsumptionAsync(decimal consumption)
        {
            try
            {
                var m = await dal.GetModel8ConsumptionAsync(consumption);
                return (m, null);
            }
            catch (Exception ex)
            {
                return (null, ex);
            }
        }

        //public List<MemberLevel> GetList(MemberLevel m)
        //{
        //    var dal = GetDAL(out bool isCreator);

        //    try
        //    {
        //        var list = dal.GetList(m);
        //        return list;
        //    }
        //    catch
        //    {
        //        return null;
        //    }
        //    finally
        //    {
        //        if (isCreator) base.CloseSqlHelper();
        //    }
        //}

        //public static List<MemberLevel> GetDic(OnlineUser user)
        //{
        //    var key = GetDicCacheKey(user.company_id);
        //    var list = CacheUtils.Get<List<MemberLevel>>(key);
        //    if (list == null)
        //    {
        //        list = new MemberLevelBLL(user).GetDic<MemberLevel>();
        //        if (list != null)
        //        {
        //            CacheUtils.Insert(key, list);
        //        }
        //    }
        //    return list;
        //}

        //public static string GetDicCacheKey(string company_id)
        //{
        //    return string.Format(Constant.CACHE_DIC, "MemberLevel", company_id);
        //}

        //public Result DeleteModel(string id)
        //{
        //    if (bookConfig.erp_version_type == ERPVersionType.NEW)
        //    {
        //        return base.DeleteModel8Id(id);
        //    }
        //    else
        //    {
        //        return base.DeleteModel8Code(id);
        //    }
        //}

        //public Result DeleteModels(string ids)
        //{
        //    if (bookConfig.erp_version_type == ERPVersionType.NEW)
        //    {
        //        return base.DeleteModel8Ids(ids);
        //    }
        //    else
        //    {
        //        return base.DeleteModel8Codes(ids);
        //    }
        //}

        ////更新使用状态
        //public Result UpdateIsUse(string id, int value)
        //{
        //    if (bookConfig.erp_version_type == ERPVersionType.NEW)
        //    {
        //        return base.UpdateIsUse8Id(id, value);
        //    }
        //    else
        //    {
        //        return base.UpdateIsUse8Code(id, value);
        //    }
        //}



    }
}
