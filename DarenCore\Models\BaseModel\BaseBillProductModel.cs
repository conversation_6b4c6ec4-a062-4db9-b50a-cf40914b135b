﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace DarenCore.Models.BaseModel
{
    public class BaseBillProductModel : BaseChildenModel
    {
        public BaseBillProductModel()
        {
            color_id = "";
            serial_no = "";
            batch_no = "";
            sterilize_no = "";
        }

        [DisplayName("商品ID")]
        [StringLength(255)]
        public string product_id { get; set; }

        [DisplayName("商品编码")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string product_code { get; set; }

        [DisplayName("商品条码")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string product_barcode { get; set; }

        [DisplayName("商品货号")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string product_number { get; set; }

        [DisplayName("商品名称")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string product_name { get; set; }

        [DisplayName("品牌")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string brand { get; set; }

        [DisplayName("季节")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string season { get; set; }

        [DisplayName("属性")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string attribute_id { get; set; }

        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string attribute_code { get; set; }

        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string attribute_name { get; set; }

        [DisplayName("特性")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string property { get; set; }

        [DisplayName("配置")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string configure { get; set; }

        [DisplayName("规格")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string specification { get; set; }

        [DisplayName("剂型")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string form { get; set; }

        [DisplayName("材料")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string material { get; set; }

        [DisplayName("产地")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string growth { get; set; }

        [DisplayName("生产厂家")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string manufacturer { get; set; }

        [DisplayName("批准文号")]
        [StringLength(100)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string approval_number { get; set; }

        [DisplayName("注册证号")]
        [StringLength(100)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string registration_number { get; set; }

        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string extra01 { get; set; }

        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string extra02 { get; set; }

        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string extra03 { get; set; }

        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string extra04 { get; set; }

        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string extra05 { get; set; }

        [DisplayName("有效期限")]
        public int expires_in { get; set; }

        [DisplayName("近效期限")]
        public int close_in { get; set; }

        [DisplayName("保修期限")]
        public int warranty_in { get; set; }

        [DisplayName("批次号")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string batch_no { get; set; }

        [DisplayName("生产日期")]
        public DateTime? manufacture_date { get; set; }

        [DisplayName("有效期至")]
        public DateTime? expires_date { get; set; }

        [DisplayName("灭菌批号")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string sterilize_no { get; set; }

        [DisplayName("灭菌日期")]
        public DateTime? sterilize_date { get; set; }

        [DisplayName("灭菌效期")]
        public DateTime? sterilize_exp { get; set; }

        [DisplayName("保修期至")]
        public DateTime? warranty_date { get; set; }

        [DisplayName("序列号")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string serial_no { get; set; }

        [DisplayName("颜色")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string color_id { get; set; }

        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string color_name { get; set; }

        [DisplayName("尺码")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string size_id { get; set; }

        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string size_name { get; set; }

        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public int size_order { get; set; }

        [DisplayName("单位")]
        [StringLength(255)]
        public string unit { get; set; }

        [DisplayName("数量")]
        public decimal quantity { get; set; }

        [DisplayName("单价")]    //也称折后价格或现价
        public decimal price { get; set; }

        [DisplayName("金额")]   //也称折后金额或现价金额
        public decimal amount { get; set; }

        [DisplayName("分组")]
        [StringLength(255)]
        public string group_id { get; set; }

        [DisplayName("成本核算方式")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string cost_mode { get; set; }

        [DisplayName("备注")]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string note { get; set; }
    }
}