﻿using Climber.Model;
using DarenApp.BLL.Dic;
using DarenApp.BLL.Stock;
using DarenApp.Models.Bill.SalesReturn;
using DarenApp.Models.Stock;
using DarenCore.Common;
using DarenCore.Extension;
using DarenCore.Interface;
using Microsoft.IdentityModel.Tokens;

namespace DarenApp.BLL.Bill.SalesReturn
{
    public class SalesReturnBuilder : BillBaseBuilder
    {
        private readonly SalesReturnMainBLL mainBLL;
        private readonly SalesReturnMain main;
        private readonly List<SalesReturnProduct> products;
        private readonly List<SalesReturnSettlement> settlements;
        private readonly IBillMainChecker<SalesReturnMain> mainChecker;
        private readonly IBillProductChecker<SalesReturnMain, SalesReturnProduct> productChecker;
        private readonly SettlementChecker.SalesReturnChecker settlementChecker = new();

        public SalesReturnBuilder(IWebAppContext ctx, SalesReturnBill bill) : base(ctx)
        {
            mainBLL = new(ctx);
            main = bill.main;
            products = bill.products;
            settlements = bill.settlements;
            mainChecker = MainChecker.CheckerFactory.Get(erpType);
            productChecker = ProductChecker.CheckerFactory.Get(erpType);
        }


        #region 建造基本信息

        /// <summary>
        /// 基本信息校验
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public override async Task<(bool, string?)> CheckMainAsync(BillBuilderType type)
        {
            if (main == null)
            {
                return (false, "单据基本信息为空！");
            }

            //新单据
            if (string.IsNullOrEmpty(main.id))
            {
                if (string.IsNullOrEmpty(main.bill_no))
                {
                    main.bill_no = await billSnBLL.GetBillNo(SalesReturnBill.bill_type);
                }
            }
            //旧单据 
            else if (type == BillBuilderType.AUDIT || type == BillBuilderType.CANCEL)
            {
                var status = await mainBLL.GetBillStatusAsync(main.id);
                if (status == -1)
                {
                    return (false, "单据状态不正确！");
                }

                //审核单据
                if (type == BillBuilderType.AUDIT && status == 1)
                {
                    return (false, "单据状态为[已审核]，请勿重复审核！");
                }

                //反审单据
                if (type == BillBuilderType.CANCEL && status == 0)
                {
                    return (false, "单据状态为[待审核]，请勿重复反审！");
                }
            }

            var bl = mainChecker.Check(main, out string? msg);
            return (bl, msg);
        }

        //基本信息初始化
        public override void InitMain()
        {
            main.bill_discount = 100;  //整单折扣
            main.sum_quantity = 0;     //总数量
            main.sum_amount = 0;       //总金额
            main.sum_tax = 0;          //总税额
            main.sum_total = 0;        //总合计(价税合计)
            main.sum_reduce = 0;       //总折扣
            main.sum_sales_cost = 0;   //总成本
            main.sum_sales_profit = 0; //总毛利
        }

        #endregion

        #region 构建商品信息

        /// <summary>
        /// 获取商品行数
        /// </summary>
        /// <returns></returns>
        public override int GetPoductCount()
        {
            return products == null ? 0 : products.Count;
        }

        /// <summary>
        /// 商品信息校验
        /// </summary>
        /// <param name="i"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        public override (int, string?, string?) CheckProduct(int i, BillBuilderType type)
        {
            var p = products[i];

            //删除空行
            if (p == null || p.product_id.IsNullOrEmpty())
            {
                products.RemoveAt(i);
                return (1, null, null);
            }

            //填要摘要
            if (i == 0)
            {
                main.abstracts = GetAbstracts(SalesReturnBill.bill_name, p.product_name);
            }

            //如果只是保存单据，则不做商品属性的检验
            if (type == BillBuilderType.SAVE)
            {
                return (0, null, null);
            }
            else
            {
                return productChecker.Check(main, products, i);
            }
        }

        //商品信息初始化
        public override void InitProduct(int i)
        {
            var p = products[i];
            p.id = ""; //用于标记新行
            if (p.warranty_in > 0)
            {
                p.warranty_date = main.bill_date.AddMonths(p.warranty_in);
            }
        }

        //商品信息计算
        public override void CalculateProduct(int i)
        {
            var p = products[i];
            main.sum_quantity += p.quantity;
            main.sum_amount += p.amount;
            main.sum_tax += p.tax;
            main.sum_total += p.total;
            main.sum_reduce += p.reduce;
            main.sum_sales_cost = (p.quantity * p.cost_price).Fix(digitsSetting.je);
            main.sum_sales_profit = (p.amount - p.sales_cost).Fix(digitsSetting.je);
        }

        #endregion

        #region 构建结算信息

        /// <summary>
        /// 获取结算信息行数
        /// </summary>
        /// <returns></returns>
        public override int GetSettlementCount()
        {
            return settlements == null ? 0 : settlements.Count;
        }

        /// <summary>
        /// /结算信息校验
        /// </summary>
        /// <param name="i"></param>
        /// <returns></returns>
        public override (int, string?, string?) CheckSettlement(int i)
        {
            return settlementChecker.Check(settlements, i);
        }

        /// <summary>
        /// 结算信息初始化
        /// </summary>
        /// <param name="i"></param>
        public override void InitSettlement(int i)
        {
            settlements[i].id = "";  //用于标记新行
        }

        #endregion

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="flag">flag==true, 保存时调用;flag==false,审核时调用</param>
        /// <returns></returns>
        protected override async Task<Result> BuildSaveAsync(bool flag)
        {
            //保存基本信息
            var r = await mainBLL.SaveModelAsync(main);
            if (r.IsFail)
            {
                return r.addErrorPosition("保存基本信息");
            }

            //保存商品信息
            if (GetPoductCount() > 0)
            {
                var pb = new SalesReturnProductBLL(ctx);
                if (!main.is_new_row)
                {
                    r = await pb.DeleteByPidAsync(main.id);
                    if (r.IsFail)
                    {
                        return r.addErrorPosition("删除旧商品信息");
                    }
                }

                r = await pb.SaveModelsAsync(products, main.id);
                if (r.IsFail)
                {
                    return r.addErrorPosition("保存商品信息");
                }
            }

            //保存结算信息
            if (GetSettlementCount() > 0)
            {
                var sb = new SalesReturnSettlementBLL(ctx);
                if (!main.is_new_row)
                {
                    r = await sb.DeleteByPidAsync(main.id);
                    if (r.IsFail)
                    {
                        return r.addErrorPosition("删除旧结算信息");
                    }
                }

                r = await sb.SaveModelsAsync(settlements, main.id);
                if (r.IsFail)
                {
                    return r.addErrorPosition("保存结算信息");
                }
            }

            //更新单号
            if (main.is_new_row)
            {
                r =  await billSnBLL.UpdateBillSn(SalesReturnBill.bill_type, main.bill_no);
                if (r.IsFail)
                {
                    return r.addErrorPosition("更新单号");
                }
            }

            if (r.IsSuccess && flag)
            {
                r.data = new { main.id, main.bill_no };
            }

            return r;
        }

        /// <summary>
        /// 审核
        /// </summary>
        /// <returns></returns>
        protected override async Task<Result> BuildAuditAsync()
        {
            var r = await UpdateStock(1);
            if (r.IsSuccess)
            {
                r.data = new
                {
                    main.id,
                    main.bill_no,
                    balance = await GetCurrentBalance(main.enterprise_id)
                };
            }
            return r;
        }

        /// <summary>
        /// 反审
        /// </summary>
        /// <returns></returns>
        protected override async Task<Result> BuildCancelAsync()
        {
            var r = await UpdateStock(0);
            if (r.IsSuccess)
            {
                r.data = new
                {
                    balance = await GetCurrentBalance(main.enterprise_id)
                };
            }
            return r;
        }


        /// <summary>
        /// 更新库存和往来余额
        /// </summary>
        /// <param name="bill_status"></param>
        /// <returns></returns>
        protected async Task<Result> UpdateStock(int bill_status)
        {
            //填充审核信息
            if (bill_status == 1)
            {
                main.auditor_id = user.user_id;
                main.auditor_name = user.user_name;
                main.audit_date = main.edit_date;
            }
            //填充反审信息
            else
            {
                main.auditor_id = null;
                main.auditor_name = null;
                main.audit_date = null;
            }

            main.bill_status = bill_status;

            //更新单据状态
            var r = await mainBLL.UpdateBillStatusAsync(main.id, main.bill_status, main.auditor_id, main.auditor_name, main.audit_date);
            if (r.IsFail)
            { 
                return r.addErrorPosition("更新单据状态");
            }

            //同步往来余额
            r = await customerBLL.UpdateCurrentBalanceAsync(main.enterprise_id, main.balance, "YINFYE", bill_status == 1);
            if (r.IsFail)
            { 
                return r.addErrorPosition("同步往来余额"); 
            }

            //同步会员积分
            r = await customerBLL.UpdateMemberBonusAsync(main.enterprise_id, main.payable, main.sum_bonus, main.use_bonus, main.use_recharge, main.bill_status == 1);
            if (r.IsFail) 
            {
                return r.addErrorPosition("同步会员积分");
            }

            //同步库存信息
            var stockBLL = new ProductStockBLL(ctx);

            //获取实际存货部门ID
            var department_id = await DepartmentBLL.GetStockDepartmentId(ctx, main.department_id);

            foreach (var p in products)
            {
                if (ErpPropJudge.IsExpense(p.cost_mode, p.attribute_id, erpType))
                {
                    continue;
                }

                var s = new ProductStock
                {
                    shop_id = main.shop_id,
                    department_id = department_id,
                    product_id = p.product_id,
                    group_id = p.group_id,
                    attribute_id = p.attribute_id,  //商品属性
                    expires_in = p.expires_in,
                    batch_no = p.batch_no,
                    store_date = main.bill_date,
                    manufacture_date = p.manufacture_date,
                    expires_date = p.expires_date,
                    sterilize_no = p.sterilize_no,
                    sterilize_date = p.sterilize_date,
                    sterilize_exp = p.sterilize_exp,
                    serial_no = p.serial_no,  //串号卡号、产品序列号
                    color_id = p.color_id,
                    color_name = p.color_name,
                    size_order = p.size_order,
                    quantity = p.quantity,
                    amount = p.amount,
                    location_no = p.location_no,
                    note = p.note
                };

                if (ErpDataRequire.IsRequireWarehouseProduct(erpType))
                {
                    s.warehouse_id = p.warehouse_id;
                    s.warehouse_name = p.warehouse_name;
                }
                else
                {
                    s.warehouse_id = main.warehouse_id;
                    s.warehouse_name = main.warehouse_name;
                }

                r = await stockBLL.UpdateStockAsync(s, p.sn, bill_status == 1, bill_status == 1);
                if (r.IsFail)
                {
                    return r.addErrorPosition("同步库存信息");
                }
            }

            return r;
        }


    }
}
