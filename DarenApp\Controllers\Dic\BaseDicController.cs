﻿using DarenCore.BLL;
using DarenCore.Common;
using Microsoft.AspNetCore.Mvc;

namespace DarenApp.Controllers.Dic
{
    /// <summary>
    /// 控制器 - 数据字典
    /// </summary>
    /// <typeparam name="M">实体类</typeparam>
    /// <typeparam name="B">业务逻辑层BLL</typeparam>
    public class BaseDicController<M, B> : BaseController where M : class where B : IControllerDicBLL<M>, IAbstractBLL, new()
    {
        private B CreateBLL()
        {
            var bll = new B();
            bll.SetContext(acs.GetContextByCurrentUser());
            return bll;
        }

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="m"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> GetList(M m)
        {
            using var bll = CreateBLL();
            var (data, ex) = await bll.GetListAsync(m);
            return Json(Result.ByException(ex, data));
        }


        /// <summary>
        /// 获取分页
        /// </summary>
        /// <param name="m"></param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">每页行数</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ContentResult> GetPage(M m, int pageIndex, int pageSize)
        {
            using var bll = CreateBLL();
            var data = await bll.GetPageJsonAsync(m, pageIndex, pageSize);
            return Content(data);
        }

        /// <summary>
        /// 保存单条数据
        /// </summary>
        /// <param name="m"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> Save([FromBody] M m)
        {
            var r = CheckModelState();
            if (r.IsSuccess)
            {
                using var bll = CreateBLL();
                r = await bll.SaveModelAsync(m);
            }
            return Json(r);
        }

        /// <summary>
        /// 保存多条数据
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<JsonResult> Saves([FromBody] List<M> list)
        {
            var r = CheckModelState();
            if (r.IsSuccess)
            {
                using var bll = CreateBLL();
                r = await bll.SaveModelsAsync(list);
            }
            return Json(r);
        }

        /// <summary>
        /// 删除单条数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> Delete(string id)
        {
            using var bll = CreateBLL();
            var r = await bll.DeleteByCodeAsync(id);
            return Json(r);
        }

        /// <summary>
        /// 删除多条数据
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> Deletes(string ids)
        {
            using var bll = CreateBLL();
            var r = await bll.DeleteByCodesAsync(ids);
            return Json(r);
        }

        /// <summary>
        /// 更新使用状态
        /// </summary>
        /// <param name="id"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> UpdateIsUse(string id, int value)
        {
            using var bll = CreateBLL();
            var r = await bll.UpdateIsUseByCodeAsync(id, value);
            return Json(r);
        }
    }
}