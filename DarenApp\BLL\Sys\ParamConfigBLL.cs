﻿using DarenApp.DAL.Sys.ParamConfigDAL;
using DarenCore.BLL.OldVersion;
using DarenCore.Common;
using DarenCore.Interface;
using DarenCore.Models;

namespace DarenApp.BLL.Sys
{
    public class ParamConfigBLL(IWebAppContext ctx) : BaseBLL<ParamConfig, IParamConfigDAL>(ctx)
    {
        public async Task<(ParamConfig?, Exception?)> GetConfig()
        {
            try
            {
                var m = await dal.GetModelAsync() ?? new();
                if (m.bonus_rate == 0)
                {
                    m.bonus_rate = 1;
                }
                m.purchase_price_mode = ErpDataConvert.GetPriceTraceMode(m.purchase_price_mode);
                m.sales_price_mode = ErpDataConvert.GetPriceTraceMode(m.sales_price_mode);
                return (m, null);
            }
            catch (Exception ex)
            {
                return (null, ex);
            }
        }


        public async Task<(ParamConfig?, Exception?)> GetModelAsync()
        {
            try
            {
                var m = await dal.GetModelAsync();
                return (m, null);
            }
            catch (Exception ex)
            {
                return (null, ex);
            }
        }


        public async Task<(string?, Exception?)> GetNotice()
        {
            try
            {
                var notice = await dal.GetNoticeAsync();
                return (notice, null);
            }
            catch (Exception ex)
            {
                return (null, ex);
            }
        }

        public async Task<(string?, Exception?)> GetAdminPasswordAsync()
        {
            try
            {
                var pwd = await dal.GetAdminPasswordAsync();
                return (pwd, null);
            }
            catch (Exception ex)
            {
                return (null, ex);
            }
        }

        public async Task<Exception?> UpdateAdminPasswordAsync(string password)
        {
            try
            {
                await dal.UpdateAdminPasswordAsync(password);
                return null;
            }
            catch (Exception ex)
            {
                return ex;
            }
        }
    }
}