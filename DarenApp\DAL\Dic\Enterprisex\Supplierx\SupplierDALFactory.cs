﻿using DarenApp.DAL.Bill.SalesReturn.Main;
using DarenCore.Common;
using DarenCore.DAL;

namespace DarenApp.DAL.Dic.Enterprisex.Supplierx
{
    public class SupplierDALFactory : IDALFactory<ISupplierDAL>
    {
        public ISupplierDAL Create(ErpType type)
        {
            return type switch
            {
                ErpType.DRUG or ErpType.INSTRUMENT => new DrugSupplierDAL(),
                _ => new CommonSupplierDAL(),
            };
        }
    }
}

