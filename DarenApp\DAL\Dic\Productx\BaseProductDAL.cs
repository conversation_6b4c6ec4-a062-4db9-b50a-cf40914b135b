﻿using DarenApp.Models.Dic;
using DarenCore.Common;
using DarenCore.DAL.OldVersion;
using Newtonsoft.Json.Linq;

namespace DarenApp.DAL.Dic.Productx
{
    public abstract class BaseProductDAL : BaseDicDAL<Product>, IProductDAL
    {
        public override string? GetTableName()
        {
            return erpType == ErpType.DRUG ? "BM_YP" : "BM_SP";
        }


        public virtual Task<JObject?> GetPageJArrayForWwzAsync(int pageIndex, int pageSize)
        {
            return Task.FromResult<JObject?>(null);
        }


        //public Product GetModel8Barcode(string barcode)
        //{
        //    var wb = DBUtilsFactory.Instance.CreateWhereBuilder(user);
        //    wb.AddItem("TM", barcode);
        //    return dao.GetModel8WhereBuilder<Product>(wb);
        //}

        //public decimal GetPrice(string product_id, string price_type)
        //{
        //    var wb = DBUtilsFactory.Instance.CreateWhereBuilder(user);
        //    wb.AddItem("BM", product_id);
        //    return dao.GetDecimal8WhereBuilder(price_type, wb);
        //}

        //public abstract string GetLimit(Product m);

        //public List<Product> GetList(Product m)
        //{
        //    return dao.GetList8Limit<Product>(GetLimit(m), "BM");
        //}

        //public string GetPage(Product m)
        //{
        //    return dao.GetPageJson(GetLimit(m), "BM", (m.pageIndex - 1) * m.pageSize, m.pageSize);
        //}

        //public string GetStockPage(Product m, string department_id, string warehouse_id)
        //{
        //    var product_id_field = bookConfig.erp_type == ERPType.DRUG ? "YPBM" : "SPBM";
        //    var sb = new StatementBuilder();
        //    sb.Add("(select " + GetTableFields() + " from " + GetTableName() + ComTools.AddWhere(GetLimit(m)) + ") as p");
        //    if (paramConfig.is_negative_stock == 1)
        //    {
        //        sb.Add("left join");
        //    }
        //    else
        //    {
        //        sb.Add("inner join");
        //    }
        //    sb.Add("(select " + product_id_field + " product_id,sum(KCSL) as quantity from KC_KCDTB");
        //    sb.AddStr("BMBM", department_id, 0, "where");
        //    sb.AddStr("CKBM", warehouse_id);
        //    if (paramConfig.is_negative_stock == 0)
        //    {
        //        sb.Add("and KCSL>0");
        //    }
        //    sb.Add("group by " + product_id_field + ") as s on p.id=s.product_id");

        //    var p = new PageParam
        //    {
        //        dataSql = "select * from " + sb.ToString(),
        //        countSql = "select count(1) from " + sb.ToString(),
        //        pageIndex = m.pageIndex,
        //        pageSize = m.pageSize,
        //        dataType = PageDataType.list,
        //    };

        //    return dao.GetPageJson(p);
        //}

        //public virtual Result UpdateCommissionInfo(ProductMarketing m)
        //{
        //    return new Result(ResultCode.ok);
        //}

    }
}
