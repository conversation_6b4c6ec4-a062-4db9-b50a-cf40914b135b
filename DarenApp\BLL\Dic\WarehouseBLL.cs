﻿using DarenApp.DAL.Dic.Warehousex;
using DarenApp.Models.Dic;
using DarenCore.BLL;
using DarenCore.BLL.OldVersion;
using DarenCore.Common;
using DarenCore.Interface;

namespace DarenApp.BLL.Dic
{
    public class WarehouseBLL : BaseDicBLL<Warehouse, IWarehouseDAL>, IControllerDicBLL<Warehouse>
    {
        public WarehouseBLL()
        {

        }

        public WarehouseBLL(IWebAppContext ctx) : base(ctx)
        {

        }

        public override async Task<(string key, string id, object? data, Exception?)> SaveModelAsync(Warehouse m, int sn)
        {
            if (string.IsNullOrEmpty(m.spell))
            {
                m.spell = GetFirstSpell(m.name);
            }

            try
            {
                await dal.SaveModelAsync(m, sn);
                return (m.key, m.id, m.spell, null);
            }
            catch (Exception ex)
            {
                return ("", "", null, ex);
            }
        }

        public Task<Result> SaveModelAsync(Warehouse m)
        {
            return base.SaveModelAsync(m, true);
        }

        public Task<Result> SaveModelsAsync(List<Warehouse> list)
        {
            return base.SaveModelsAsync(list, true);
        }


        public async Task<(Warehouse?,Exception?)> GetFirst(string department_id)
        {
            try
            {
                var m = await dal.GetFirstAsync(department_id);
                return (m, null);
            }
            catch (Exception ex)
            {
                return (null, ex);
            }
        }
    }
}
