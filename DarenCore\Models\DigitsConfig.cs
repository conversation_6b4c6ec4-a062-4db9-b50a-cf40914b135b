﻿using DarenCore.Models.BaseModel;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace DarenCore.Models
{
    //系统小数位配置
    public class DigitsConfig: BaseIdModel
    {
        public DigitsConfig()
        {

        }

        [DisplayName("序号")]
        public int sn { get; set; }

        [DisplayName("项目")]
        [StringLength(255)]
        public string item { get; set; }

        [DisplayName("位数")]
        public int digits { get; set; }

        [DisplayName("范围")]
        [StringLength(255)]
        public string range { get; set; }

    }
}