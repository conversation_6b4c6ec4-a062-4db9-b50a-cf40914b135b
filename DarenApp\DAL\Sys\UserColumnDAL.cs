﻿using DarenApp.Models.Sys;
using DarenCore.Common;
using DarenCore.DAL.OldVersion;
using DarenCore.DBUtils.SqlBuilder.Interfaces;

namespace DarenApp.DAL.Sys
{
    public class UserColumnDAL : BaseDAL<UserColumn>
    {

        public override string? GetTableName()
        {
            return "GG_TB_BL";
        }

        public override Task<Result> SaveModelAsync(UserColumn m, int _)
        {
            IInsertUpdateBuilder<UserColumn> iu;
            if (m.is_new_row)
            {
                iu = CreateInsertBuilder();
                iu.AddItemStr("id", m.id);
            }
            else
            {
                iu = CreateUpdateBuilder();
                iu.AddWhereItemString("id", m.id);
            }
            iu.AddItemStr("employee_id", m.employee_id);
            iu.AddItemInt("sn", m.sn);
            iu.AddItemStr("component", m.component);
            iu.AddItemStr("column_name", m.column_name);
            iu.AddItemStr("column_title", m.column_title);
            iu.AddItemInt("width", m.width);
            iu.AddItemInt("is_visible", m.is_visible);
            iu.AddItemDateTime("add_date", m.add_date);

            return SaveModelAsync(iu, SaveDataType.PRIMARY);
        }

        public Task<int> DeleteColumnsAsync(string component)
        {
            var wb = CreateWhereBuilder();
            wb.AddItemString("component", component);
            wb.AddItemString("employee_id", user.user_id);
            return dao.DeleteByWhereBuilderAsync(wb);
        }

        public Task<List<UserColumn>> GetListAsync(string component)
        {
            var wb = CreateWhereBuilder();
            wb.AddItemString("component", component);
            wb.AddItemString("employee_id", user.user_id);
            return dao.GetListByWhereBuilderAsync(wb, "sn");
        }

        public override string GetTableFields()
        {
            return "*";
        }

    }
}