﻿using DarenApp.DAL.Sys;
using DarenApp.Models.Sys;
using DarenCore.BLL.OldVersion;
using DarenCore.Interface;

namespace DarenApp.BLL.Sys
{
    public class TableMainBLL(IWebAppContext ctx) : BaseBLL<TableMain, TableMainDAL>(ctx)
    {

        public async Task<(TableMain?, Exception?)> GetModelByNameAsync(string name)
        {
            try
            {
                var m = await dal.GetModelByNameAsync(name);
                return (m, null);
            }
            catch (Exception ex)
            {
                return (null, ex);
            }
        }
    }
}
