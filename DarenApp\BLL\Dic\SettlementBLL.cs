﻿using DarenApp.DAL.Dic;
using DarenApp.Models.Dic;
using DarenCore.BLL;
using DarenCore.BLL.OldVersion;
using DarenCore.Common;
using DarenCore.Interface;

namespace DarenApp.BLL.Dic
{
    public class SettlementBLL : BaseDicBLL<Settlement, SettlementDAL>, IControllerDicBLL<Settlement>
    {
        public SettlementBLL()
        {

        }

        public SettlementBLL(IWebAppContext ctx) : base(ctx)
        {

        }

        public override async Task<(string key, string id, object? data, Exception?)> SaveModelAsync(Settlement m, int sn)
        {
            try
            {
                await dal.SaveModelAsync(m, sn);
                return (m.key, m.id, null, null);
            }
            catch (Exception ex)
            {
                return ("", "", null, ex);
            }
        }

        public Task<Result> SaveModelAsync(Settlement m)
        {
            return base.SaveModelAsync(m, true);
        }

        public Task<Result> SaveModelsAsync(List<Settlement> list)
        {
            return base.SaveModelsAsync(list, true);
        }

    }
}
