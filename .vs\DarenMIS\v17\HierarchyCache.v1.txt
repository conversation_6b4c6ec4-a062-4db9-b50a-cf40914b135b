﻿++解决方案 'DarenMIS' ‎ (8 个项目，共 8 个)
i:{00000000-0000-0000-0000-000000000000}:DarenMIS.sln
++DarenWeb
i:{00000000-0000-0000-0000-000000000000}:DarenWeb
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:>3721
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:>3689
++Connected Services 
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:>3652
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:>3655
i:{6787278c-1204-49f7-a3a4-e088656acb21}:>3651
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:>3654
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:>3653
++Properties
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\properties\
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\properties\
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\properties\
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\properties\
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\properties\
++依赖项
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:>3668
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:>3661
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:>3665
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:>3663
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:>3662
i:{6787278c-1204-49f7-a3a4-e088656acb21}:>3667
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:>3666
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:>3664
++BLL
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\bll\
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:e:\cursortest\darenmis20250624(1)\darenshare\bll\
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\bll\
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\
++Common
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\common\
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\controllers\common\
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\common\
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\common\
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\common\
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\controllers\common\
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\common\
++Controllers
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\controllers\
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\controllers\
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\controllers\
++DAL
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\dal\
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:e:\cursortest\darenmis20250624(1)\darenshare\dal\
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dal\
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\
++Services
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\services\
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:e:\cursortest\darenmis20250624(1)\darenshare\services\
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\services\
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\services\
++Views
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\views\
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\views\
++appsettings.json
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\appsettings.json
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\appsettings.json
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\appsettings.json
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\appsettings.json
++DarenWEB.http
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\darenweb.http
++favicon.ico
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\favicon.ico
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\favicon.ico
++Main.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\main.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\main.cs
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\main.cs
++Program.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\program.cs
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\program.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\program.cs
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\program.cs
++未发现任何服务依赖项
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:>3656
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:>3659
i:{6787278c-1204-49f7-a3a4-e088656acb21}:>3658
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:>3657
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:>3660
++launchSettings.json
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\properties\launchsettings.json
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\properties\launchsettings.json
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\properties\launchsettings.json
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\properties\launchsettings.json
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\properties\launchsettings.json
++包
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:>3823
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:>3811
i:{6787278c-1204-49f7-a3a4-e088656acb21}:>3884
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:>3881
++程序集
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:>3828
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:>3769
i:{6787278c-1204-49f7-a3a4-e088656acb21}:>3896
++分析器
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:>3782
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:>3777
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:>3729
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:>3728
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:>3671
i:{6787278c-1204-49f7-a3a4-e088656acb21}:>3831
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:>3838
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:>3694
++框架
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:>3820
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:>3806
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:>3766
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:>3758
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:>3686
i:{6787278c-1204-49f7-a3a4-e088656acb21}:>3877
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:>3876
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:>3715
++项目
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:>3774
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:>3773
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:>3719
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:>3718
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:>3669
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:>3830
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:>3688
++Com
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\bll\com\
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\dal\com\
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:e:\cursortest\darenmis20250624(1)\darenweb.models\com\
++Dic
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\bll\dic\
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\controllers\dic\
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\dal\dic\
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\dic\
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:e:\cursortest\darenmis20250624(1)\darenweb.models\dic\
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\dic\
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\controllers\dic\
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\interface\dic\
++DL
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\bll\dl\
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\dal\dl\
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:e:\cursortest\darenmis20250624(1)\darenweb.models\dl\
++OW
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\bll\ow\
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\dal\ow\
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\views\ow\
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:e:\cursortest\darenmis20250624(1)\darenweb.models\ow\
++Sys
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\bll\sys\
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\controllers\sys\
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\dal\sys\
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\sys\
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:e:\cursortest\darenmis20250624(1)\darenweb.models\sys\
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\sys\
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\sys\
++User
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\bll\user\
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\dal\user\
++Constant.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\common\constant.cs
++UserPower.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\common\userpower.cs
++WebApp.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\common\webapp.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\common\webapp.cs
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\webapp.cs
++WebAppMaster.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\common\webappmaster.cs
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\webappmaster.cs
++BaseController.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\controllers\basecontroller.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\controllers\basecontroller.cs
++OwController.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\controllers\owcontroller.cs
++BackupService.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\services\backupservice.cs
++LogService.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\services\logservice.cs
++OnlineUserService.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\services\onlineuserservice.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\services\onlineuserservice.cs
++WebContextService.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\services\webcontextservice.cs
++Shared
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\views\shared\
++WebHome
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\views\webhome\
++appsettings.Development.json
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\appsettings.development.json
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\appsettings.development.json
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\appsettings.development.json
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\appsettings.development.json
++Microsoft.AspNetCore.OpenApi (9.0.0)
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:>3827
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:>3889
++Microsoft.EntityFrameworkCore (9.0.0)
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:>3824
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:>3813
i:{6787278c-1204-49f7-a3a4-e088656acb21}:>3888
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:>3883
++Swashbuckle.AspNetCore (7.2.0)
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:>3825
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:>3885
++Swashbuckle.AspNetCore.Swagger (7.2.0)
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:>3826
++Newtonsoft.Json
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:>3829
i:{6787278c-1204-49f7-a3a4-e088656acb21}:>3897
++Microsoft.AspNetCore.Analyzers
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk.web\analyzers\cs\microsoft.aspnetcore.analyzers.dll
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk.web\analyzers\cs\microsoft.aspnetcore.analyzers.dll
i:{6787278c-1204-49f7-a3a4-e088656acb21}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk.web\analyzers\cs\microsoft.aspnetcore.analyzers.dll
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk.web\analyzers\cs\microsoft.aspnetcore.analyzers.dll
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk.web\analyzers\cs\microsoft.aspnetcore.analyzers.dll
++Microsoft.AspNetCore.App.Analyzers
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.aspnetcore.app.analyzers.dll
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.aspnetcore.app.analyzers.dll
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.aspnetcore.app.analyzers.dll
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.aspnetcore.app.analyzers.dll
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.aspnetcore.app.analyzers.dll
i:{6787278c-1204-49f7-a3a4-e088656acb21}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.aspnetcore.app.analyzers.dll
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.aspnetcore.app.analyzers.dll
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.aspnetcore.app.analyzers.dll
++Microsoft.AspNetCore.App.CodeFixes
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.aspnetcore.app.codefixes.dll
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.aspnetcore.app.codefixes.dll
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.aspnetcore.app.codefixes.dll
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.aspnetcore.app.codefixes.dll
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.aspnetcore.app.codefixes.dll
i:{6787278c-1204-49f7-a3a4-e088656acb21}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.aspnetcore.app.codefixes.dll
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.aspnetcore.app.codefixes.dll
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.aspnetcore.app.codefixes.dll
++Microsoft.AspNetCore.Components.Analyzers
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.aspnetcore.components.analyzers.dll
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.aspnetcore.components.analyzers.dll
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.aspnetcore.components.analyzers.dll
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.aspnetcore.components.analyzers.dll
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.aspnetcore.components.analyzers.dll
i:{6787278c-1204-49f7-a3a4-e088656acb21}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.aspnetcore.components.analyzers.dll
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.aspnetcore.components.analyzers.dll
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.aspnetcore.components.analyzers.dll
++Microsoft.AspNetCore.Mvc.Analyzers
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk.web\analyzers\cs\microsoft.aspnetcore.mvc.analyzers.dll
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk.web\analyzers\cs\microsoft.aspnetcore.mvc.analyzers.dll
i:{6787278c-1204-49f7-a3a4-e088656acb21}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk.web\analyzers\cs\microsoft.aspnetcore.mvc.analyzers.dll
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk.web\analyzers\cs\microsoft.aspnetcore.mvc.analyzers.dll
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk.web\analyzers\cs\microsoft.aspnetcore.mvc.analyzers.dll
++Microsoft.AspNetCore.Razor.Utilities.Shared
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk.razor\source-generators\microsoft.aspnetcore.razor.utilities.shared.dll
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk.razor\source-generators\microsoft.aspnetcore.razor.utilities.shared.dll
i:{6787278c-1204-49f7-a3a4-e088656acb21}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk.razor\source-generators\microsoft.aspnetcore.razor.utilities.shared.dll
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk.razor\source-generators\microsoft.aspnetcore.razor.utilities.shared.dll
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk.razor\source-generators\microsoft.aspnetcore.razor.utilities.shared.dll
++Microsoft.CodeAnalysis.CSharp.NetAnalyzers
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{6787278c-1204-49f7-a3a4-e088656acb21}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
++Microsoft.CodeAnalysis.NetAnalyzers
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{6787278c-1204-49f7-a3a4-e088656acb21}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
++Microsoft.CodeAnalysis.Razor.Compiler
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk.razor\source-generators\microsoft.codeanalysis.razor.compiler.dll
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk.razor\source-generators\microsoft.codeanalysis.razor.compiler.dll
i:{6787278c-1204-49f7-a3a4-e088656acb21}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk.razor\source-generators\microsoft.codeanalysis.razor.compiler.dll
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk.razor\source-generators\microsoft.codeanalysis.razor.compiler.dll
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk.razor\source-generators\microsoft.codeanalysis.razor.compiler.dll
++Microsoft.EntityFrameworkCore.Analyzers
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:c:\users\<USER>\.nuget\packages\microsoft.entityframeworkcore.analyzers\9.0.0\analyzers\dotnet\cs\microsoft.entityframeworkcore.analyzers.dll
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:c:\users\<USER>\.nuget\packages\microsoft.entityframeworkcore.analyzers\9.0.0\analyzers\dotnet\cs\microsoft.entityframeworkcore.analyzers.dll
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:c:\users\<USER>\.nuget\packages\microsoft.entityframeworkcore.analyzers\9.0.0\analyzers\dotnet\cs\microsoft.entityframeworkcore.analyzers.dll
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:c:\users\<USER>\.nuget\packages\microsoft.entityframeworkcore.analyzers\9.0.0\analyzers\dotnet\cs\microsoft.entityframeworkcore.analyzers.dll
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:c:\users\<USER>\.nuget\packages\microsoft.entityframeworkcore.analyzers\9.0.0\analyzers\dotnet\cs\microsoft.entityframeworkcore.analyzers.dll
i:{6787278c-1204-49f7-a3a4-e088656acb21}:c:\users\<USER>\.nuget\packages\microsoft.entityframeworkcore.analyzers\9.0.0\analyzers\dotnet\cs\microsoft.entityframeworkcore.analyzers.dll
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:c:\users\<USER>\.nuget\packages\microsoft.entityframeworkcore.analyzers\9.0.0\analyzers\dotnet\cs\microsoft.entityframeworkcore.analyzers.dll
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:c:\users\<USER>\.nuget\packages\microsoft.entityframeworkcore.analyzers\9.0.0\analyzers\dotnet\cs\microsoft.entityframeworkcore.analyzers.dll
++Microsoft.Extensions.Logging.Generators
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
i:{6787278c-1204-49f7-a3a4-e088656acb21}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
++Microsoft.Extensions.ObjectPool
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk.razor\source-generators\microsoft.extensions.objectpool.dll
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk.razor\source-generators\microsoft.extensions.objectpool.dll
i:{6787278c-1204-49f7-a3a4-e088656acb21}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk.razor\source-generators\microsoft.extensions.objectpool.dll
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk.razor\source-generators\microsoft.extensions.objectpool.dll
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk.razor\source-generators\microsoft.extensions.objectpool.dll
++Microsoft.Extensions.Options.SourceGeneration
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
i:{6787278c-1204-49f7-a3a4-e088656acb21}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.7\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
++Microsoft.Interop.ComInterfaceGenerator
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{6787278c-1204-49f7-a3a4-e088656acb21}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
++Microsoft.Interop.JavaScript.JSImportGenerator
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{6787278c-1204-49f7-a3a4-e088656acb21}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
++Microsoft.Interop.LibraryImportGenerator
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{6787278c-1204-49f7-a3a4-e088656acb21}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
++Microsoft.Interop.SourceGeneration
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{6787278c-1204-49f7-a3a4-e088656acb21}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
++System.Collections.Immutable
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk.razor\source-generators\system.collections.immutable.dll
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk.razor\source-generators\system.collections.immutable.dll
i:{6787278c-1204-49f7-a3a4-e088656acb21}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk.razor\source-generators\system.collections.immutable.dll
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk.razor\source-generators\system.collections.immutable.dll
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:c:\program files\dotnet\sdk\9.0.302\sdks\microsoft.net.sdk.razor\source-generators\system.collections.immutable.dll
++System.Text.Json.SourceGeneration
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{6787278c-1204-49f7-a3a4-e088656acb21}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
++System.Text.RegularExpressions.Generator
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{6787278c-1204-49f7-a3a4-e088656acb21}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.7\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
++Microsoft.AspNetCore.App
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:>3821
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:>3767
i:{6787278c-1204-49f7-a3a4-e088656acb21}:>3880
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:>3878
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:>3716
++Microsoft.NETCore.App
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:>3822
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:>3809
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:>3768
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:>3761
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:>3687
i:{6787278c-1204-49f7-a3a4-e088656acb21}:>3882
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:>3879
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:>3717
++DarenApp.Models
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:>3776
i:{00000000-0000-0000-0000-000000000000}:DarenApp.Models
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:>3723
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:>3720
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:>3832
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:>3690
++DarenCore
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:>3781
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:>3775
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:>3724
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:>3722
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:>3670
i:{00000000-0000-0000-0000-000000000000}:DarenCore
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:>3836
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:>3691
++DarenShare
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:>3778
i:{00000000-0000-0000-0000-000000000000}:DarenShare
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:>3835
++SmsUserBLL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\bll\com\smsuserbll.cs
++DicExtension.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\bll\dic\dicextension.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\dic\dicextension.cs
++DistrictBLL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\bll\dic\districtbll.cs
++DlBackupBLL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\bll\dl\dlbackupbll.cs
++DlDatabaseBLL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\bll\dl\dldatabasebll.cs
++DlService.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\bll\dl\dlservice.cs
++DlUtils.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\bll\dl\dlutils.cs
++OwCourseBLL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\bll\ow\owcoursebll.cs
++OwCourseCatalogBLL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\bll\ow\owcoursecatalogbll.cs
++OwCustomerBLL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\bll\ow\owcustomerbll.cs
++OwMonitorBLL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\bll\ow\owmonitorbll.cs
++OwService.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\bll\ow\owservice.cs
++OwSoftBrowseBLL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\bll\ow\owsoftbrowsebll.cs
++OwSoftCategoryBLL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\bll\ow\owsoftcategorybll.cs
++OwSoftDownloadBLL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\bll\ow\owsoftdownloadbll.cs
++OwSoftInfoBLL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\bll\ow\owsoftinfobll.cs
++OwVisitorInfoBLL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\bll\ow\owvisitorinfobll.cs
++ApiAuthBLL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\bll\sys\apiauthbll.cs
++SysAccessTokenBLL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\bll\sys\sysaccesstokenbll.cs
++SysAppConfigBLL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\bll\sys\sysappconfigbll.cs
++SysCollectFeeBLL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\bll\sys\syscollectfeebll.cs
++SysEmployeeBLL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\bll\sys\sysemployeebll.cs
++SysHostBLL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\bll\sys\syshostbll.cs
++SysLogBLL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\bll\sys\syslogbll.cs
++SysNavigationBLL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\bll\sys\sysnavigationbll.cs
++SysRoleBLL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\bll\sys\sysrolebll.cs
++SysRolePowerBLL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\bll\sys\sysrolepowerbll.cs
++CommonController.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\controllers\common\commoncontroller.cs
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\controllers\commoncontroller.cs
++DlController.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\controllers\common\dlcontroller.cs
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\controllers\dlcontroller.cs
++HomeController.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\controllers\common\homecontroller.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\controllers\common\homecontroller.cs
++EmployeeController.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\controllers\dic\employeecontroller.cs
++CompanyController.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\controllers\sys\companycontroller.cs
++NavigationController.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\controllers\sys\navigationcontroller.cs
++SmsUserDAL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\dal\com\smsuserdal.cs
++DistictDAL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\dal\dic\distictdal.cs
++DlBackupDAL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\dal\dl\dlbackupdal.cs
++DlDatabaseDAL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\dal\dl\dldatabasedal.cs
++OwCourseCatalogDAL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\dal\ow\owcoursecatalogdal.cs
++OwCourseDAL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\dal\ow\owcoursedal.cs
++OwCustomerDAL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\dal\ow\owcustomerdal.cs
++OwMonitorDAL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\dal\ow\owmonitordal.cs
++OwSoftBrowseDAL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\dal\ow\owsoftbrowsedal.cs
++OwSoftCategoryDAL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\dal\ow\owsoftcategorydal.cs
++OwSoftDownloadDAL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\dal\ow\owsoftdownloaddal.cs
++OwSoftInfoDAL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\dal\ow\owsoftinfodal.cs
++OwVisitorInfoDAL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\dal\ow\owvisitorinfodal.cs
++ApiAuthDAL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\dal\sys\apiauthdal.cs
++SysAccessTokenDAL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\dal\sys\sysaccesstokendal.cs
++SysAppConfigDAL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\dal\sys\sysappconfigdal.cs
++SysCollectFeeDAL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\dal\sys\syscollectfeedal.cs
++SysEmployeeDAL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\dal\sys\sysemployeedal.cs
++SysHostDAL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\dal\sys\syshostdal.cs
++SysLogDAL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\dal\sys\syslogdal.cs
++SysNavigationDAL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\dal\sys\sysnavigationdal.cs
++SysRoleDAL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\dal\sys\sysroledal.cs
++SysRolePowerDAL.cs
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\dal\sys\sysrolepowerdal.cs
++_ViewImports.cshtml
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\views\ow\_viewimports.cshtml
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\views\shared\_viewimports.cshtml
++_ViewStart.cshtml
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\views\ow\_viewstart.cshtml
++About.cshtml
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\views\ow\about.cshtml
++Agent.cshtml
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\views\ow\agent.cshtml
++Case.cshtml
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\views\ow\case.cshtml
++Course.cshtml
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\views\ow\course.cshtml
++DownloadApp.cshtml
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\views\ow\downloadapp.cshtml
++GenReg.cshtml
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\views\ow\genreg.cshtml
++Index.cshtml
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\views\ow\index.cshtml
++Product.cshtml
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\views\ow\product.cshtml
++Purchase.cshtml
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\views\ow\purchase.cshtml
++Service.cshtml
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\views\ow\service.cshtml
++SoftIntroduce.cshtml
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\views\ow\softintroduce.cshtml
++_Layout1.cshtml
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\views\shared\_layout1.cshtml
++_Layout2.cshtml
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\views\shared\_layout2.cshtml
++_Layout3.cshtml
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\views\shared\_layout3.cshtml
++Error.cshtml
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\views\webhome\error.cshtml
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\views\apphome\error.cshtml
++Error404.cshtml
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\views\webhome\error404.cshtml
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\views\apphome\error404.cshtml
++Error500.cshtml
i:{0a2e48ce-4e69-4e53-b703-5660b3621105}:e:\cursortest\darenmis20250624(1)\darenweb\views\webhome\error500.cshtml
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\views\apphome\error500.cshtml
++Bill
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\bill\
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\
++Stock
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\stock\
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\stock\
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\stock\
++PurchaseIn
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\bill\purchasein\
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\purchasein\
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\purchasein\
++SalesOrder
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\bill\salesorder\
++SalesOut
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\bill\salesout\
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\salesout\
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\salesout\
++SalesReturn
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\bill\salesreturn\
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\salesreturn\
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\salesreturn\
++BillSn.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\bill\billsn.cs
++BusinessScope.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\dic\businessscope.cs
++Customer.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\dic\customer.cs
++Department.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\dic\department.cs
++District.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\dic\district.cs
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:e:\cursortest\darenmis20250624(1)\darenweb.models\dic\district.cs
++Employee.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\dic\employee.cs
++Enterprise.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\dic\enterprise.cs
++EnterpriseCategory.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\dic\enterprisecategory.cs
++Manufacturer.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\dic\manufacturer.cs
++MemberLevel.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\dic\memberlevel.cs
++Product.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\dic\product.cs
++ProductBrand.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\dic\productbrand.cs
++ProductCategory.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\dic\productcategory.cs
++ProductExtend.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\dic\productextend.cs
++ProductForm.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\dic\productform.cs
++ProductGrowth.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\dic\productgrowth.cs
++ProductMarketing.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\dic\productmarketing.cs
++ProductPrice.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\dic\productprice.cs
++ProductUnit.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\dic\productunit.cs
++Settlement.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\dic\settlement.cs
++Supplier.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\dic\supplier.cs
++Warehouse.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\dic\warehouse.cs
++ProductStock.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\stock\productstock.cs
++ProductStockPrice.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\stock\productstockprice.cs
++TableColumn.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\sys\tablecolumn.cs
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:e:\cursortest\darenmis20250624(1)\darenweb.models\sys\tablecolumn.cs
++TableMain.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\sys\tablemain.cs
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:e:\cursortest\darenmis20250624(1)\darenweb.models\sys\tablemain.cs
++UserColumn.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\sys\usercolumn.cs
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:e:\cursortest\darenmis20250624(1)\darenweb.models\sys\usercolumn.cs
++Microsoft.AspNetCore.Mvc.NewtonsoftJson (9.0.0)
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:>3814
i:{6787278c-1204-49f7-a3a4-e088656acb21}:>3892
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:>3886
++PurchaseInBill.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\bill\purchasein\purchaseinbill.cs
++PurchaseInMain.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\bill\purchasein\purchaseinmain.cs
++PurchaseInProduct.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\bill\purchasein\purchaseinproduct.cs
++PurchaseInSettlement.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\bill\purchasein\purchaseinsettlement.cs
++SalesOrderBill.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\bill\salesorder\salesorderbill.cs
++SalesOrderMain.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\bill\salesorder\salesordermain.cs
++SalesOrderProduct.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\bill\salesorder\salesorderproduct.cs
++SalesOrderSettlement.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\bill\salesorder\salesordersettlement.cs
++SalesOutBill.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\bill\salesout\salesoutbill.cs
++SalesOutMain.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\bill\salesout\salesoutmain.cs
++SalesOutProduct.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\bill\salesout\salesoutproduct.cs
++SalesOutSettlement.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\bill\salesout\salesoutsettlement.cs
++SalesReturnBill.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\bill\salesreturn\salesreturnbill.cs
++SalesReturnMain.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\bill\salesreturn\salesreturnmain.cs
++SalesReturnProduct.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\bill\salesreturn\salesreturnproduct.cs
++SalesReturnSettlement.cs
i:{ef9374c6-8699-4f90-bff1-ce6277040ba6}:e:\cursortest\darenmis20250624(1)\darenapp.models\bill\salesreturn\salesreturnsettlement.cs
++DarenApi
i:{00000000-0000-0000-0000-000000000000}:DarenApi
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:>3693
++Models
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\models\
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\onlinestore\models\
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\models\
++OnlineStore
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\onlinestore\
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\models\onlinestore\
++ApiTokenFactory.cs
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\apitokenfactory.cs
++ShanximbApi.cs
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\common\shanximbapi.cs
++WwzAPI.cs
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\common\wwzapi.cs
++WwzService.cs
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\common\wwzservice.cs
++YunuoApi.cs
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\common\yunuoapi.cs
++ApiController.cs
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\controllers\apicontroller.cs
++DrugTraceController.cs
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\controllers\drugtracecontroller.cs
++ErpController.cs
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\controllers\erpcontroller.cs
++ShanximbController.cs
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\controllers\shanximbcontroller.cs
++WwzController.cs
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\controllers\wwzcontroller.cs
++YunuoController.cs
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\controllers\yunuocontroller.cs
++BaseApi.cs
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\onlinestore\baseapi.cs
++ElemeApi.cs
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\onlinestore\elemeapi.cs
++IApi.cs
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\onlinestore\iapi.cs
++JingdongApi.cs
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\onlinestore\jingdongapi.cs
++PinduoduoApi.cs
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\onlinestore\pinduoduoapi.cs
++ShangouApi.cs
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\onlinestore\shangouapi.cs
++TaobaoApi.cs
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\onlinestore\taobaoapi.cs
++ElemeSdk
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:>3770
++ShangouSdk
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:>3772
++TopSdk
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:>3771
++DarenApp
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:>3725
i:{00000000-0000-0000-0000-000000000000}:DarenApp
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:>3692
++DarenWeb.Models
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:>3727
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:>3726
i:{00000000-0000-0000-0000-000000000000}:DarenWeb.Models
++PushOrder.cs
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\models\onlinestore\pushorder.cs
++Store.cs
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\models\onlinestore\store.cs
++AccessTokenInfo.cs
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\onlinestore\models\accesstokeninfo.cs
++AccessTokenResult.cs
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\onlinestore\models\accesstokenresult.cs
++OrderResult.cs
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\onlinestore\models\orderresult.cs
++OrdersResult.cs
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\onlinestore\models\ordersresult.cs
++StoreApiConfig.cs
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\onlinestore\models\storeapiconfig.cs
++SyncProductTask.cs
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\onlinestore\models\syncproducttask.cs
++Ware.cs
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\onlinestore\models\ware.cs
++WareSku.cs
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\onlinestore\models\waresku.cs
++WareStock.cs
i:{8ac92d3b-10ad-44f0-9ccc-0443e878afe7}:e:\cursortest\darenmis20250624(1)\darenapi\onlinestore\models\warestock.cs
++DbContext
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:e:\cursortest\darenmis20250624(1)\darenshare\dbcontext\
++DbTableDicBLL.cs
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:e:\cursortest\darenmis20250624(1)\darenshare\bll\dbtabledicbll.cs
++ErpInfoBLL.cs
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:e:\cursortest\darenmis20250624(1)\darenshare\bll\erpinfobll.cs
++JsonFileDicBLL.cs
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:e:\cursortest\darenmis20250624(1)\darenshare\bll\jsonfiledicbll.cs
++OnlineUserBLL.cs
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:e:\cursortest\darenmis20250624(1)\darenshare\bll\onlineuserbll.cs
++SysBookBLL.cs
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:e:\cursortest\darenmis20250624(1)\darenshare\bll\sysbookbll.cs
++SysCompanyBLL.cs
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:e:\cursortest\darenmis20250624(1)\darenshare\bll\syscompanybll.cs
++ErpInfoDAL.cs
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:e:\cursortest\darenmis20250624(1)\darenshare\dal\erpinfodal.cs
++OnlineUserDAL.cs
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:e:\cursortest\darenmis20250624(1)\darenshare\dal\onlineuserdal.cs
++SysBookDAL.cs
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:e:\cursortest\darenmis20250624(1)\darenshare\dal\sysbookdal.cs
++SysCompanyDAL.cs
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:e:\cursortest\darenmis20250624(1)\darenshare\dal\syscompanydal.cs
++DbContextFactory.cs
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:e:\cursortest\darenmis20250624(1)\darenshare\dbcontext\dbcontextfactory.cs
++DrugDbContext.cs
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:e:\cursortest\darenmis20250624(1)\darenshare\dbcontext\drugdbcontext.cs
++InstrumentContext.cs
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:e:\cursortest\darenmis20250624(1)\darenshare\dbcontext\instrumentcontext.cs
++WebDbContext.cs
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:e:\cursortest\darenmis20250624(1)\darenshare\dbcontext\webdbcontext.cs
++BookService.cs
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:e:\cursortest\darenmis20250624(1)\darenshare\services\bookservice.cs
++OnlineUserCleanService.cs
i:{63412004-a5ab-4db4-abdc-fe7d0a17a61b}:e:\cursortest\darenmis20250624(1)\darenshare\services\onlineusercleanservice.cs
++SmsUser.cs
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:e:\cursortest\darenmis20250624(1)\darenweb.models\com\smsuser.cs
++DlBackup.cs
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:e:\cursortest\darenmis20250624(1)\darenweb.models\dl\dlbackup.cs
++DlDatabase.cs
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:e:\cursortest\darenmis20250624(1)\darenweb.models\dl\dldatabase.cs
++DlSpace.cs
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:e:\cursortest\darenmis20250624(1)\darenweb.models\dl\dlspace.cs
++OwCourse.cs
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:e:\cursortest\darenmis20250624(1)\darenweb.models\ow\owcourse.cs
++OwCourseCatalog.cs
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:e:\cursortest\darenmis20250624(1)\darenweb.models\ow\owcoursecatalog.cs
++OwCustomer.cs
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:e:\cursortest\darenmis20250624(1)\darenweb.models\ow\owcustomer.cs
++OwMonitor.cs
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:e:\cursortest\darenmis20250624(1)\darenweb.models\ow\owmonitor.cs
++OwSoftBrowse.cs
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:e:\cursortest\darenmis20250624(1)\darenweb.models\ow\owsoftbrowse.cs
++OwSoftCategory.cs
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:e:\cursortest\darenmis20250624(1)\darenweb.models\ow\owsoftcategory.cs
++OwSoftDownload.cs
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:e:\cursortest\darenmis20250624(1)\darenweb.models\ow\owsoftdownload.cs
++OwSoftInfo.cs
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:e:\cursortest\darenmis20250624(1)\darenweb.models\ow\owsoftinfo.cs
++OwSoftIntroduceDTO.cs
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:e:\cursortest\darenmis20250624(1)\darenweb.models\ow\owsoftintroducedto.cs
++OwVisitorInfo.cs
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:e:\cursortest\darenmis20250624(1)\darenweb.models\ow\owvisitorinfo.cs
++OwWebConfig.cs
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:e:\cursortest\darenmis20250624(1)\darenweb.models\ow\owwebconfig.cs
++ApiAuth.cs
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:e:\cursortest\darenmis20250624(1)\darenweb.models\sys\apiauth.cs
++SysAccessToken.cs
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:e:\cursortest\darenmis20250624(1)\darenweb.models\sys\sysaccesstoken.cs
++SysBook.cs
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:e:\cursortest\darenmis20250624(1)\darenweb.models\sys\sysbook.cs
++SysCollectFee.cs
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:e:\cursortest\darenmis20250624(1)\darenweb.models\sys\syscollectfee.cs
++SysCompany.cs
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:e:\cursortest\darenmis20250624(1)\darenweb.models\sys\syscompany.cs
++SysEmployee.cs
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:e:\cursortest\darenmis20250624(1)\darenweb.models\sys\sysemployee.cs
++SysHost.cs
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:e:\cursortest\darenmis20250624(1)\darenweb.models\sys\syshost.cs
++SysLog.cs
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:e:\cursortest\darenmis20250624(1)\darenweb.models\sys\syslog.cs
++SysNavigation.cs
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:e:\cursortest\darenmis20250624(1)\darenweb.models\sys\sysnavigation.cs
++SysRole.cs
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:e:\cursortest\darenmis20250624(1)\darenweb.models\sys\sysrole.cs
++SysRolePower.cs
i:{e0d8955d-5fdc-43d7-a39a-4368c41c19b6}:e:\cursortest\darenmis20250624(1)\darenweb.models\sys\sysrolepower.cs
++Abstract
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\abstract\
++DAO
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dao\
++DBUtils
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dbutils\
++Extension
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\extension\
++Helper
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\helper\
++Interface
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\interface\
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\interface\
++Middlewares
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\middlewares\
++Utils
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\utils\
++AbstractAppConfig.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\abstract\abstractappconfig.cs
++AbstractAppOptions.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\abstract\abstractappoptions.cs
++AbstractAppSetting.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\abstract\abstractappsetting.cs
++AbstractContext.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\abstract\abstractcontext.cs
++AbstractController.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\abstract\abstractcontroller.cs
++AbstractDbContext.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\abstract\abstractdbcontext.cs
++AbstractDisposable.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\abstract\abstractdisposable.cs
++AbstractOnlineUserService.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\abstract\abstractonlineuserservice.cs
++AbstractWebApp.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\abstract\abstractwebapp.cs
++NewVersion
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\bll\newversion\
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dal\newversion\
++OldVersion
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\bll\oldversion\
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dal\oldversion\
++AbstractBLL.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\bll\abstractbll.cs
++IAbstractBLL.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\bll\iabstractbll.cs
++IBaseDicBLL.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\bll\ibasedicbll.cs
++IControllerDicBLL.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\bll\icontrollerdicbll.cs
++AppConfig.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\common\appconfig.cs
++AppOptions.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\common\appoptions.cs
++AppSetting.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\common\appsetting.cs
++DbConfig.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\common\dbconfig.cs
++Enums.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\common\enums.cs
++ErpDataCheck.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\common\erpdatacheck.cs
++ErpDataConvert.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\common\erpdataconvert.cs
++ErpDataRequire.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\common\erpdatarequire.cs
++ErpLimit.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\common\erplimit.cs
++ErpPropJudge.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\common\erppropjudge.cs
++ErpUserPower.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\common\erpuserpower.cs
++KeyValueDic.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\common\keyvaluedic.cs
++Language.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\common\language.cs
++PaginationParam.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\common\paginationparam.cs
++Result.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\common\result.cs
++ResultBase.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\common\resultbase.cs
++Resultx.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\common\resultx.cs
++ServiceOptions.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\common\serviceoptions.cs
++ValueLabelDic.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\common\valuelabeldic.cs
++WebAppContext.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\common\webappcontext.cs
++AbstractDAL.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dal\abstractdal.cs
++IAbstractDAL.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dal\iabstractdal.cs
++IDALFactory.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dal\idalfactory.cs
++EfcDAO.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dao\efcdao.cs
++SqlDAO.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dao\sqldao.cs
++SqlBuilder
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dbutils\sqlbuilder\
++SqlHelper
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dbutils\sqlhelper\
++AttributeAssistant.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dbutils\attributeassistant.cs
++CommonAssistant.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dbutils\commonassistant.cs
++DataReaderAssistant.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dbutils\datareaderassistant.cs
++DBUtilsFactory.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dbutils\dbutilsfactory.cs
++EfcHelper.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dbutils\efchelper.cs
++IDBUtilFactory.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dbutils\idbutilfactory.cs
++SentenceBuilder.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dbutils\sentencebuilder.cs
++StatementBuilder.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dbutils\statementbuilder.cs
++CustomCache.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\extension\customcache.cs
++CustomLoggerProvider.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\extension\customloggerprovider.cs
++ErrorRespose.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\extension\errorrespose.cs
++FilteredTextWriter.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\extension\filteredtextwriter.cs
++Ini.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\extension\ini.cs
++Map.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\extension\map.cs
++SafeMap.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\extension\safemap.cs
++Singleton.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\extension\singleton.cs
++SystemExtension.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\extension\systemextension.cs
++Taskx.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\extension\taskx.cs
++AppConfigHelper.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\helper\appconfighelper.cs
++AppSettingHelper.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\helper\appsettinghelper.cs
++CacheHelper.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\helper\cachehelper.cs
++JwtHelper.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\helper\jwthelper.cs
++IAbstractContext.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\interface\iabstractcontext.cs
++IAbstractDbContextFactory.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\interface\iabstractdbcontextfactory.cs
++IApiTokenFactory.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\interface\iapitokenfactory.cs
++IBookService.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\interface\ibookservice.cs
++ILogService.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\interface\ilogservice.cs
++IParamOptionService.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\interface\iparamoptionservice.cs
++IWebAppContext.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\interface\iwebappcontext.cs
++DailyBackgroundService.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\middlewares\dailybackgroundservice.cs
++IntervalBackgroundService.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\middlewares\intervalbackgroundservice.cs
++ReverseProxyMiddleware.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\middlewares\reverseproxymiddleware.cs
++TokenMiddleware.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\middlewares\tokenmiddleware.cs
++BaseModel
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\models\basemodel\
++DTO
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\models\dto\
++Interfaces
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\models\interfaces\
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dbutils\sqlbuilder\interfaces\
++DigitsConfig.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\models\digitsconfig.cs
++DigitsSetting.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\models\digitssetting.cs
++ErpInfo.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\models\erpinfo.cs
++OnlineUser.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\models\onlineuser.cs
++ParamConfig.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\models\paramconfig.cs
++ParamOption.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\models\paramoption.cs
++SysAppConfig.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\models\sysappconfig.cs
++CacheService.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\services\cacheservice.cs
++DicService.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\services\dicservice.cs
++WebAppContextService.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\services\webappcontextservice.cs
++ComUtils.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\utils\comutils.cs
++ConsoleUtils.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\utils\consoleutils.cs
++ConvertUtils.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\utils\convertutils.cs
++DateUtils.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\utils\dateutils.cs
++EncryptUtils.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\utils\encryptutils.cs
++FileUtils.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\utils\fileutils.cs
++GzipUtils.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\utils\gziputils.cs
++HttpUtils.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\utils\httputils.cs
++IdUtils.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\utils\idutils.cs
++ImageUtils.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\utils\imageutils.cs
++JsonUtils.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\utils\jsonutils.cs
++LogUtils.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\utils\logutils.cs
++MathUtils.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\utils\mathutils.cs
++ModelUtils.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\utils\modelutils.cs
++PaginationUtils.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\utils\paginationutils.cs
++SharpZipUtils.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\utils\sharpziputils.cs
++StreamUtils.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\utils\streamutils.cs
++TimerUtils.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\utils\timerutils.cs
++UrlBuilder.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\utils\urlbuilder.cs
++Microsoft.EntityFrameworkCore.SqlServer (9.0.0)
i:{6787278c-1204-49f7-a3a4-e088656acb21}:>3890
++Microsoft.Extensions.Caching.Memory (9.0.0)
i:{6787278c-1204-49f7-a3a4-e088656acb21}:>3895
++NLog (5.3.4)
i:{6787278c-1204-49f7-a3a4-e088656acb21}:>3893
++NPinyin.Core (3.0.0)
i:{6787278c-1204-49f7-a3a4-e088656acb21}:>3891
++SharpZipLib (1.4.2)
i:{6787278c-1204-49f7-a3a4-e088656acb21}:>3894
++System.Drawing.Common (9.0.1)
i:{6787278c-1204-49f7-a3a4-e088656acb21}:>3887
++ThoughtWorks.QRCode
i:{6787278c-1204-49f7-a3a4-e088656acb21}:>3898
++BaseBLL.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\bll\newversion\basebll.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\bll\oldversion\basebll.cs
++BaseDicBLL.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\bll\newversion\basedicbll.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\bll\oldversion\basedicbll.cs
++BaseDAL.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dal\newversion\basedal.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dal\oldversion\basedal.cs
++BaseDicDAL.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dal\newversion\basedicdal.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dal\oldversion\basedicdal.cs
++IBaseDAL.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dal\newversion\ibasedal.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dal\oldversion\ibasedal.cs
++IBaseDicDAL.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dal\newversion\ibasedicdal.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dal\oldversion\ibasedicdal.cs
++BaseBillMainDAL.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dal\oldversion\basebillmaindal.cs
++IBaseBillMainDAL.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dal\oldversion\ibasebillmaindal.cs
++MsSqlbuilder
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dbutils\sqlbuilder\mssqlbuilder\
++ISqlHelper.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dbutils\sqlhelper\isqlhelper.cs
++MsSqlHelper.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dbutils\sqlhelper\mssqlhelper.cs
++BaseBillMainModel.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\models\basemodel\basebillmainmodel.cs
++BaseBillPaymentModel.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\models\basemodel\basebillpaymentmodel.cs
++BaseBillProductModel.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\models\basemodel\basebillproductmodel.cs
++BaseBillSettlementModel.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\models\basemodel\basebillsettlementmodel.cs
++BaseChildenModel.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\models\basemodel\basechildenmodel.cs
++BaseDateModel.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\models\basemodel\basedatemodel.cs
++BaseDicModel.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\models\basemodel\basedicmodel.cs
++BaseIdModel.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\models\basemodel\baseidmodel.cs
++BaseModel.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\models\basemodel\basemodel.cs
++BaseParentModel.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\models\basemodel\baseparentmodel.cs
++BaseTreeModel.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\models\basemodel\basetreemodel.cs
++KeyDTO.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\models\dto\keydto.cs
++PageDateDTO.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\models\dto\pagedatedto.cs
++PageDTO.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\models\dto\pagedto.cs
++SaveResultDicDTO.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\models\dto\saveresultdicdto.cs
++SaveResultDTO.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\models\dto\saveresultdto.cs
++IAddDate.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\models\interfaces\iadddate.cs
++IEditDate.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\models\interfaces\ieditdate.cs
++IID.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\models\interfaces\iid.cs
++IOperator.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\models\interfaces\ioperator.cs
++ISN.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\models\interfaces\isn.cs
++IDeleteBuilder.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dbutils\sqlbuilder\interfaces\ideletebuilder.cs
++IInsertUpdateBuilder.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dbutils\sqlbuilder\interfaces\iinsertupdatebuilder.cs
++IParamBuilder.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dbutils\sqlbuilder\interfaces\iparambuilder.cs
++ISelectBuilder.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dbutils\sqlbuilder\interfaces\iselectbuilder.cs
++IWhereBuilder.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dbutils\sqlbuilder\interfaces\iwherebuilder.cs
++DeleteBuilder.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dbutils\sqlbuilder\mssqlbuilder\deletebuilder.cs
++InsertBuilder.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dbutils\sqlbuilder\mssqlbuilder\insertbuilder.cs
++ParamBuilder.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dbutils\sqlbuilder\mssqlbuilder\parambuilder.cs
++SelectBuilder.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dbutils\sqlbuilder\mssqlbuilder\selectbuilder.cs
++UpdateBuilder.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dbutils\sqlbuilder\mssqlbuilder\updatebuilder.cs
++WhereBuilder.cs
i:{6787278c-1204-49f7-a3a4-e088656acb21}:e:\cursortest\darenmis20250624(1)\darencore\dbutils\sqlbuilder\mssqlbuilder\wherebuilder.cs
++ProductStockBLL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\stock\productstockbll.cs
++DigitsConfigBLL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\sys\digitsconfigbll.cs
++ParamConfigBLL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\sys\paramconfigbll.cs
++TableBLL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\sys\tablebll.cs
++TableColumnBLL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\sys\tablecolumnbll.cs
++TableMainBLL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\sys\tablemainbll.cs
++UserColumnBLL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\sys\usercolumnbll.cs
++Doc
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\doc\
++BackgroundServices.txt
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\doc\backgroundservices.txt
++DbContext 实体更新.txt
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\doc\dbcontext 实体更新.txt
++DbContextPool.txt
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\doc\dbcontextpool.txt
++JWT.txt
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\doc\jwt.txt
++Net Core中的Options使用.txt
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\doc\net core中的options使用.txt
++处理null值.txt
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\doc\处理null值.txt
++动态注册服务.txt
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\doc\动态注册服务.txt
++动态注入依赖.txt
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\doc\动态注入依赖.txt
++如何自定义静态文件目录、默认主页、和文件浏览目.txt
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\doc\如何自定义静态文件目录、默认主页、和文件浏览目.txt
++小知识.txt
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\doc\小知识.txt
++中间件传值.txt
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\doc\中间件传值.txt
++中间件获取控制器属性-全局错误中间件.txt
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\doc\中间件获取控制器属性-全局错误中间件.txt
++StaticFiles
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\staticfiles\
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\
++.editorconfig
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\.editorconfig
++DarenMIS.http
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\darenmis.http
++SettlementChecker
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\settlementchecker\
++BillBaseBuilder.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\billbasebuilder.cs
++BillDirector.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\billdirector.cs
++BillMainBaseBLL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\billmainbasebll.cs
++BillSnBLL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\billsnbll.cs
++IBillBuilder.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\ibillbuilder.cs
++IBillMainChecker.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\ibillmainchecker.cs
++IBillProductChecker.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\ibillproductchecker.cs
++CustomerBLL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\dic\customerbll.cs
++DepartmentBLL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\dic\departmentbll.cs
++EmployeeBLL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\dic\employeebll.cs
++MemberLevelBLL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\dic\memberlevelbll.cs
++ProductBLL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\dic\productbll.cs
++ProductUnitBLL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\dic\productunitbll.cs
++SettlementBLL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\dic\settlementbll.cs
++SupplierBLL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\dic\supplierbll.cs
++WarehouseBLL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\dic\warehousebll.cs
++DALFactoryExtension.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dalfactoryextension.cs
++AppContextService.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\services\appcontextservice.cs
++ParamOptionService.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\services\paramoptionservice.cs
++dic
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\staticfiles\dic\
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\dic\
++AppHome
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\views\apphome\
++MainChecker
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\purchasein\mainchecker\
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\salesout\mainchecker\
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\salesreturn\mainchecker\
++ProductChecker
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\purchasein\productchecker\
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\salesout\productchecker\
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\salesreturn\productchecker\
++PurchaseInBuilder.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\purchasein\purchaseinbuilder.cs
++PurchaseInMainBLL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\purchasein\purchaseinmainbll.cs
++PurchaseInProductBLL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\purchasein\purchaseinproductbll.cs
++PurchaseInService.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\purchasein\purchaseinservice.cs
++PurchaseInSettlementBLL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\purchasein\purchaseinsettlementbll.cs
++SalesOutBuilder.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\salesout\salesoutbuilder.cs
++SalesOutMainBLL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\salesout\salesoutmainbll.cs
++SalesOutProductBLL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\salesout\salesoutproductbll.cs
++SalesOutService.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\salesout\salesoutservice.cs
++SalesOutSettlementBLL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\salesout\salesoutsettlementbll.cs
++SalesReturnBuilder.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\salesreturn\salesreturnbuilder.cs
++SalesReturnMainBLL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\salesreturn\salesreturnmainbll.cs
++SalesReturnProductBLL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\salesreturn\salesreturnproductbll.cs
++SalesReturnService.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\salesreturn\salesreturnservice.cs
++SalesReturnSettlementBLL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\salesreturn\salesreturnsettlementbll.cs
++PurchaseInChecker.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\settlementchecker\purchaseinchecker.cs
++SalesOutChecker.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\settlementchecker\salesoutchecker.cs
++SalesReturnChecker.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\settlementchecker\salesreturnchecker.cs
++DicController.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\controllers\common\diccontroller.cs
++BaseDicController.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\controllers\dic\basediccontroller.cs
++ProductUnitController.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\controllers\dic\productunitcontroller.cs
++BillSnDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\billsndal.cs
++Departmentx
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\departmentx\
++Employeex
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\employeex\
++Enterprisex
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\enterprisex\
++Productx
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\productx\
++Warehousex
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\warehousex\
++SettlementDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\settlementdal.cs
++Base
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\interface\base\
++ProductStockDAL
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\stock\productstockdal\
++ParamConfigDAL
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\sys\paramconfigdal\
++DigitsConfigDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\sys\digitsconfigdal.cs
++TableColumnDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\sys\tablecolumndal.cs
++TableMainDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\sys\tablemaindal.cs
++UserColumnDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\sys\usercolumndal.cs
++AcceptType.json
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\staticfiles\dic\accepttype.json
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\dic\accepttype.json
++ApiType.json
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\staticfiles\dic\apitype.json
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\dic\apitype.json
++AppType.json
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\staticfiles\dic\apptype.json
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\dic\apptype.json
++BusinessScopeType.json
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\staticfiles\dic\businessscopetype.json
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\dic\businessscopetype.json
++DepartmentType.json
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\staticfiles\dic\departmenttype.json
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\dic\departmenttype.json
++DrugFuncCate.json
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\staticfiles\dic\drugfunccate.json
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\dic\drugfunccate.json
++DrugType.json
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\staticfiles\dic\drugtype.json
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\dic\drugtype.json
++Education.json
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\staticfiles\dic\education.json
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\dic\education.json
++InvoiceType.json
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\staticfiles\dic\invoicetype.json
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\dic\invoicetype.json
++MaintainType.json
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\staticfiles\dic\maintaintype.json
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\dic\maintaintype.json
++PartnerType.json
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\staticfiles\dic\partnertype.json
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\dic\partnertype.json
++Professional.json
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\staticfiles\dic\professional.json
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\dic\professional.json
++SalesPriceType.json
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\staticfiles\dic\salespricetype.json
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\dic\salespricetype.json
++SpecialDrugCate.json
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\staticfiles\dic\specialdrugcate.json
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\dic\specialdrugcate.json
++StorageCondition.json
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\staticfiles\dic\storagecondition.json
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\dic\storagecondition.json
++TransportCondition.json
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\staticfiles\dic\transportcondition.json
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\dic\transportcondition.json
++CheckerFactory.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\purchasein\mainchecker\checkerfactory.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\purchasein\productchecker\checkerfactory.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\salesout\mainchecker\checkerfactory.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\salesout\productchecker\checkerfactory.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\salesreturn\mainchecker\checkerfactory.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\salesreturn\productchecker\checkerfactory.cs
++CommonChecker.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\purchasein\mainchecker\commonchecker.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\purchasein\productchecker\commonchecker.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\salesout\mainchecker\commonchecker.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\salesout\productchecker\commonchecker.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\salesreturn\mainchecker\commonchecker.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\salesreturn\productchecker\commonchecker.cs
++DrugChecker.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\purchasein\mainchecker\drugchecker.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\purchasein\productchecker\drugchecker.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\salesout\mainchecker\drugchecker.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\salesout\productchecker\drugchecker.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\salesreturn\mainchecker\drugchecker.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\bll\bill\salesreturn\productchecker\drugchecker.cs
++Main
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\purchasein\main\
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\salesout\main\
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\salesreturn\main\
++Product
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\purchasein\product\
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\salesout\product\
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\salesreturn\product\
++PurchaseInSettlementDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\purchasein\purchaseinsettlementdal.cs
++SalesOutSettlementDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\salesout\salesoutsettlementdal.cs
++SalesReturnSettlementDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\salesreturn\salesreturnsettlementdal.cs
++MemberLevelDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\common\memberleveldal.cs
++ProductUnitDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\common\productunitdal.cs
++BaseDepartmentDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\departmentx\basedepartmentdal.cs
++CommonDepartmentDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\departmentx\commondepartmentdal.cs
++DepartmentDALFactory.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\departmentx\departmentdalfactory.cs
++IDepartmentDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\departmentx\idepartmentdal.cs
++BaseEmployeeDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\employeex\baseemployeedal.cs
++CommonEmployeeDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\employeex\commonemployeedal.cs
++EmployeeDALFactory.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\employeex\employeedalfactory.cs
++IEmployeeDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\employeex\iemployeedal.cs
++Customerx
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\enterprisex\customerx\
++Supplierx
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\enterprisex\supplierx\
++EnterpriseDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\enterprisex\enterprisedal.cs
++IEnterpriseDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\enterprisex\ienterprisedal.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\interface\dic\ienterprisedal.cs
++BaseProductDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\productx\baseproductdal.cs
++DrugProductDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\productx\drugproductdal.cs
++IProductDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\productx\iproductdal.cs
++ProductDALFactory.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\productx\productdalfactory.cs
++BaseWarehouseDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\warehousex\basewarehousedal.cs
++CommonWarehouseDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\warehousex\commonwarehousedal.cs
++DrugWarehouseDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\warehousex\drugwarehousedal.cs
++IWarehouseDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\warehousex\iwarehousedal.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\interface\dic\iwarehousedal.cs
++WarehouseDALFactory.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\warehousex\warehousedalfactory.cs
++IBaseDAL1.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\interface\base\ibasedal1.cs
++IBaseDicDAL1.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\interface\base\ibasedicdal1.cs
++IBusinessScopeDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\interface\dic\ibusinessscopedal.cs
++ICustomerDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\interface\dic\icustomerdal.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\enterprisex\customerx\icustomerdal.cs
++IDistrictDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\interface\dic\idistrictdal.cs
++IEnterpriseCategoryDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\interface\dic\ienterprisecategorydal.cs
++IManufacturerDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\interface\dic\imanufacturerdal.cs
++IMemberLevelDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\interface\dic\imemberleveldal.cs
++IProductBrandDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\interface\dic\iproductbranddal.cs
++IProductCategoryDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\interface\dic\iproductcategorydal.cs
++IProductExtendDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\interface\dic\iproductextenddal.cs
++IProductFormDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\interface\dic\iproductformdal.cs
++IProductGrowthDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\interface\dic\iproductgrowthdal.cs
++IProductMarketingDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\interface\dic\iproductmarketingdal.cs
++IProductUnitDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\interface\dic\iproductunitdal.cs
++ISettlementDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\interface\dic\isettlementdal.cs
++ISupplierDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\interface\dic\isupplierdal.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\enterprisex\supplierx\isupplierdal.cs
++BaseProductStockDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\stock\productstockdal\baseproductstockdal.cs
++CommonProductStockDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\stock\productstockdal\commonproductstockdal.cs
++DrugProductStockDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\stock\productstockdal\drugproductstockdal.cs
++InstrumentProductStockDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\stock\productstockdal\instrumentproductstockdal.cs
++IProductStockDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\stock\productstockdal\iproductstockdal.cs
++ProductStockDALFactory.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\stock\productstockdal\productstockdalfactory.cs
++BaseParamConfigDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\sys\paramconfigdal\baseparamconfigdal.cs
++CommonParamConfigDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\sys\paramconfigdal\commonparamconfigdal.cs
++InstrumentParamConfigDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\sys\paramconfigdal\instrumentparamconfigdal.cs
++IParamConfigDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\sys\paramconfigdal\iparamconfigdal.cs
++ParamConfigDALFactory.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\sys\paramconfigdal\paramconfigdalfactory.cs
++BasePurchaseInMainDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\purchasein\main\basepurchaseinmaindal.cs
++CommonPurchaseInMainDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\purchasein\main\commonpurchaseinmaindal.cs
++IPurchaseInMainDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\purchasein\main\ipurchaseinmaindal.cs
++BasePurchaseInProductDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\purchasein\product\basepurchaseinproductdal.cs
++DrugPurchaseInProductDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\purchasein\product\drugpurchaseinproductdal.cs
++InstrumentPurchaseInProductDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\purchasein\product\instrumentpurchaseinproductdal.cs
++IPurchaseInProductDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\purchasein\product\ipurchaseinproductdal.cs
++BaseSalesOutMainDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\salesout\main\basesalesoutmaindal.cs
++DrugSalesOutMainDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\salesout\main\drugsalesoutmaindal.cs
++InstrumentSalesOutMainDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\salesout\main\instrumentsalesoutmaindal.cs
++ISalesOutMainDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\salesout\main\isalesoutmaindal.cs
++SalesOutMainDALFactory.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\salesout\main\salesoutmaindalfactory.cs
++BaseSalesOutProductDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\salesout\product\basesalesoutproductdal.cs
++DrugSalesOutProductDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\salesout\product\drugsalesoutproductdal.cs
++InstrumentSalesOutProductDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\salesout\product\instrumentsalesoutproductdal.cs
++ISalesOutProductDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\salesout\product\isalesoutproductdal.cs
++SalesOutProductDALFactory.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\salesout\product\salesoutproductdalfactory.cs
++BaseSalesReturnMainDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\salesreturn\main\basesalesreturnmaindal.cs
++DrugSalesReturnMainDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\salesreturn\main\drugsalesreturnmaindal.cs
++InstrumentSalesReturnMainDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\salesreturn\main\instrumentsalesreturnmaindal.cs
++ISalesReturnMainDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\salesreturn\main\isalesreturnmaindal.cs
++SalesReturnMainDALFactory.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\salesreturn\main\salesreturnmaindalfactory.cs
++BaseSalesReturnProductDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\salesreturn\product\basesalesreturnproductdal.cs
++DrugSalesReturnProductDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\salesreturn\product\drugsalesreturnproductdal.cs
++InstrumentSalesReturnProductDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\salesreturn\product\instrumentsalesreturnproductdal.cs
++ISalesReturnProductDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\salesreturn\product\isalesreturnproductdal.cs
++SalesReturnProductDALFactory.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\bill\salesreturn\product\salesreturnproductdalfactory.cs
++BaseCustomerDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\enterprisex\customerx\basecustomerdal.cs
++CustomerDALFactory.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\enterprisex\customerx\customerdalfactory.cs
++DrugCustomerDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\enterprisex\customerx\drugcustomerdal.cs
++BaseSupplierDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\enterprisex\supplierx\basesupplierdal.cs
++CommonSupplierDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\enterprisex\supplierx\commonsupplierdal.cs
++DrugSupplierDAL.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\enterprisex\supplierx\drugsupplierdal.cs
++SupplierDALFactory.cs
i:{cd008736-229d-457d-9ecd-e4f9c918f266}:e:\cursortest\darenmis20250624(1)\darenapp\dal\dic\enterprisex\supplierx\supplierdalfactory.cs
++DarenMain
i:{00000000-0000-0000-0000-000000000000}:DarenMain
++Option
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\option\
++Config.ini
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\option\config.ini
++Config.json
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\option\config.json
++NLog.config
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\option\nlog.config
++Setting.ini
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\option\setting.ini
++Setting.json
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\option\setting.json
++css
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\css\
++download
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\download\
++img
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\
++lib
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\
++src
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\src\
++upload
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\upload\
++robots.txt
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\robots.txt
++ow.css
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\css\ow.css
++tools
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\download\tools\
++速拓
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\download\速拓\
++bg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\bg\
++bg-mobile
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\bg-mobile\
++card
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\card\
++global
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\global\
++icon
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\icon\
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\icon\
++interface
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\
++logo
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\logo\
++ow
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\
++weixin
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\weixin\
++wx-shop
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\wx-shop\
++axios
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\axios\
++bootstrap
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\bootstrap\
++element-plus
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\element-plus\
++fonts
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\fonts\
++vue
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\vue\
++HoverBox.js
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\src\hoverbox.js
++Http.js
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\src\http.js
++ImgBox.js
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\src\imgbox.js
++ow-app.js
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\src\ow-app.js
++ow-core.js
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\src\ow-core.js
++ow-utils.js
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\src\ow-utils.js
++远程协助工具(TeamViewer).rar
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\download\tools\远程协助工具(teamviewer).rar
++j_files
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\download\速拓\j_files\
++bg-mirror.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\bg\bg-mirror.png
++bg-selected.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\bg\bg-selected.png
++bg-mobile-footer-default2968da.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\bg-mobile\bg-mobile-footer-default2968da.png
++bg-mobile-head-default2968da.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\bg-mobile\bg-mobile-head-default2968da.png
++1.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\card\1.png
++10.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\card\10.png
++11.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\card\11.png
++12.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\card\12.png
++13.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\card\13.png
++14.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\card\14.png
++15.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\card\15.png
++16.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\card\16.png
++17.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\card\17.png
++18.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\card\18.png
++19.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\card\19.png
++2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\card\2.png
++20.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\card\20.png
++21.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\card\21.png
++22.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\card\22.png
++23.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\card\23.png
++3.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\card\3.png
++4.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\card\4.png
++5.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\card\5.png
++6.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\card\6.png
++7.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\card\7.png
++8.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\card\8.png
++9.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\card\9.png
++card.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\card\card.png
++card_push_mass.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\card\card_push_mass.jpg
++card_push_menu.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\card\card_push_menu.jpg
++card_push_news.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\card\card_push_news.jpg
++card_push_qrcode.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\card\card_push_qrcode.jpg
++card_push_reply.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\card\card_push_reply.jpg
++card_tpl.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\card\card_tpl.png
++member_card_control.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\card\member_card_control.png
++banner-bg.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\global\banner-bg.png
++icon-video.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\global\icon-video.png
++icon-voice.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\global\icon-voice.png
++images.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\global\images.png
++loading.gif
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\global\loading.gif
++nohead-middle.gif
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\global\nohead-middle.gif
++noimg-blur.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\global\noimg-blur.png
++noimg-focus.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\global\noimg-focus.png
++nopic-large.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\global\nopic-large.png
++nopic-middle.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\global\nopic-middle.png
++nopic-small.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\global\nopic-small.png
++select-arrow.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\global\select-arrow.png
++weixin-large.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\global\weixin-large.png
++weixin-small.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\global\weixin-small.png
++chat-icon.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\icon\chat-icon.png
++mob-icon.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\icon\mob-icon.png
++msg-board-add.gif
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\icon\msg-board-add.gif
++msg-board-dot.gif
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\icon\msg-board-dot.gif
++msg-board-line.gif
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\icon\msg-board-line.gif
++pc-footer-phone4.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\icon\pc-footer-phone4.png
++pc-footer-qq4.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\icon\pc-footer-qq4.png
++pc-footer-tele4.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\icon\pc-footer-tele4.png
++pc-footer-wx4.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\icon\pc-footer-wx4.png
++position-icon.svg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\icon\position-icon.svg
++scan-icon.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\icon\scan-icon.png
++sms-icon.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\icon\sms-icon.png
++tele-icon.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\icon\tele-icon.png
++box.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\box.png
++DC_Window_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\dc_window_2.png
++GSP_SYPZ_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\gsp_sypz_2.png
++GSP_SYPZ_QX_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\gsp_sypz_qx_2.png
++GSP_SYPZ_YY_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\gsp_sypz_yy_2.png
++GSP_SYQY_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\gsp_syqy_2.png
++GSP_SYQY_QX_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\gsp_syqy_qx_2.png
++GSP_SYQY_YY_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\gsp_syqy_yy_2.png
++JZ_Window_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\jz_window_2.png
++Login_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\login_2.png
++Navigate_ck_wz_1.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\navigate_ck_wz_1.png
++Navigate_CY_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\navigate_cy_2.png
++Navigate_FZ_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\navigate_fz_2.png
++Navigate_Public_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\navigate_public_2.png
++Navigate_QX_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\navigate_qx_2.png
++Navigate_YY_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\navigate_yy_2.png
++PosWindow_Bird_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\poswindow_bird_2.png
++PosWindow_CL_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\poswindow_cl_2.png
++PosWindow_CS_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\poswindow_cs_2.png
++PosWindow_cy_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\poswindow_cy_2.png
++PosWindow_DDC_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\poswindow_ddc_2.png
++PosWindow_DJ_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\poswindow_dj_2.png
++PosWindow_DN_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\poswindow_dn_2.png
++PosWindow_fz_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\poswindow_fz_2.png
++PosWindow_HG_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\poswindow_hg_2.png
++PosWindow_HZ_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\poswindow_hz_2.png
++PosWindow_JD_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\poswindow_jd_2.png
++PosWindow_JF_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\poswindow_jf_2.png
++PosWindow_JJ_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\poswindow_jj_2.png
++PosWindow_JXC_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\poswindow_jxc_2.png
++PosWindow_LXC_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\poswindow_lxc_2.png
++PosWindow_MY_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\poswindow_my_2.png
++PosWindow_MZ_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\poswindow_mz_2.png
++PosWindow_NZ_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\poswindow_nz_2.png
++PosWindow_PG_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\poswindow_pg_2.png
++PosWindow_PJ_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\poswindow_pj_2.png
++PosWindow_public_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\poswindow_public_2.png
++PosWindow_QH_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\poswindow_qh_2.png
++PosWindow_QX_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\poswindow_qx_2.png
++PosWindow_RZ_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\poswindow_rz_2.png
++PosWindow_S_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\poswindow_s_2.png
++PosWindow_SJ_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\poswindow_sj_2.png
++PosWindow_SP_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\poswindow_sp_2.png
++PosWindow_T_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\poswindow_t_2.png
++PosWindow_TC_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\poswindow_tc_2.png
++PosWindow_TS_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\poswindow_ts_2.png
++PosWindow_WJ_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\poswindow_wj_2.png
++PosWindow_WT_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\poswindow_wt_2.png
++PosWindow_YX_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\poswindow_yx_2.png
++PosWindow_yy_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\poswindow_yy_2.png
++VipWindow_2.JPG
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\vipwindow_2.jpg
++VipWindow_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\vipwindow_2.png
++WldwWindow_1.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\wldwwindow_1.jpg
++WldwWindow_2.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\wldwwindow_2.jpg
++WldwWindow_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\wldwwindow_2.png
++XJSK_1.JPG
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\xjsk_1.jpg
++XJSK_2.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\xjsk_2.jpg
++XJSK_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\xjsk_2.png
++YGDJ_1.JPG
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\ygdj_1.jpg
++YGDJ_2.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\ygdj_2.jpg
++YGDJ_2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\interface\ygdj_2.png
++logo16.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\logo\logo16.png
++logo32.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\logo\logo32.png
++banks
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\banks\
++carousel
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\carousel\
++case
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\case\
++person
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\person\
++about-icon-1.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\about-icon-1.jpg
++about-icon-2.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\about-icon-2.jpg
++about-icon-3.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\about-icon-3.jpg
++about-icon-4.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\about-icon-4.jpg
++agent-1.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\agent-1.png
++agent-2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\agent-2.png
++agent-3.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\agent-3.png
++agent-4.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\agent-4.png
++contact-member.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\contact-member.png
++contact-qq.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\contact-qq.png
++contact-tel.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\contact-tel.png
++download_app_cater.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\download_app_cater.png
++download_app_palm.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\download_app_palm.png
++favicon-大任.ico
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\favicon-大任.ico
++favicon-精诚.ico
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\favicon-精诚.ico
++favicon-速拓.ico
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\favicon-速拓.ico
++favicon-速腾.ico
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\favicon-速腾.ico
++logo-fastop999.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\logo-fastop999.png
++logo-大任.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\logo-大任.png
++logo-精诚.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\logo-精诚.png
++logo-速拓(有网址).png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\logo-速拓(有网址).png
++logo-速拓.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\logo-速拓.png
++logo-速拓1.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\logo-速拓1.png
++logo-速腾.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\logo-速腾.png
++nav-affix.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\nav-affix.png
++purchase-buy-way.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\purchase-buy-way.png
++service-1.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\service-1.png
++service-10.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\service-10.png
++service-11.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\service-11.png
++service-2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\service-2.png
++service-3.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\service-3.png
++service-4.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\service-4.png
++service-5.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\service-5.png
++service-6.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\service-6.png
++service-7.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\service-7.png
++service-8.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\service-8.png
++service-9.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\service-9.png
++营业执照-速腾.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\营业执照-速腾.jpg
++audio.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\weixin\audio.png
++msg_tab_z3a7b39.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\weixin\msg_tab_z3a7b39.png
++plus.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\weixin\plus.png
++qrcode-hbst.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\weixin\qrcode-hbst.png
++wechat15337269166.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\weixin\wechat15337269166.jpg
++wechat17386137560.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\weixin\wechat17386137560.jpg
++wechat17386137561.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\weixin\wechat17386137561.jpg
++wechat17386137581.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\weixin\wechat17386137581.jpg
++wechat17386137582.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\weixin\wechat17386137582.jpg
++wechat18062503271.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\weixin\wechat18062503271.jpg
++wechat18062503275.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\weixin\wechat18062503275.jpg
++wechat18062503276.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\weixin\wechat18062503276.jpg
++wechat18963943119.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\weixin\wechat18963943119.jpg
++wechat19971952236.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\weixin\wechat19971952236.jpg
++weixin_oauth.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\weixin\weixin_oauth.png
++weixin15337269166.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\weixin\weixin15337269166.jpg
++weixin17386137560.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\weixin\weixin17386137560.jpg
++weixin17386137561.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\weixin\weixin17386137561.jpg
++weixin17386137581.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\weixin\weixin17386137581.jpg
++weixin17386137582.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\weixin\weixin17386137582.jpg
++weixin18062503271.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\weixin\weixin18062503271.jpg
++weixin18062503275.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\weixin\weixin18062503275.jpg
++weixin18062503276.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\weixin\weixin18062503276.jpg
++weixin18963943119.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\weixin\weixin18963943119.jpg
++product-all.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\wx-shop\product-all.png
++browser
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\axios\browser\
++esm
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\axios\esm\
++node
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\axios\node\
++axios.js
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\axios\axios.js
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\axios\esm\axios.js
++bootstrap.css
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\bootstrap\bootstrap.css
++font-awesome.min.css
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\bootstrap\font-awesome.min.css
++dist
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\element-plus\dist\
++es
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\element-plus\es\
++FontAwesome.otf
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\fonts\fontawesome.otf
++fontawesome-webfont.eot
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\fonts\fontawesome-webfont.eot
++fontawesome-webfont.svg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\fonts\fontawesome-webfont.svg
++fontawesome-webfont.ttf
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\fonts\fontawesome-webfont.ttf
++fontawesome-webfont.woff
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\fonts\fontawesome-webfont.woff
++fontawesome-webfont.woff2
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\fonts\fontawesome-webfont.woff2
++glyphicons-halflings-regular.eot
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\fonts\glyphicons-halflings-regular.eot
++glyphicons-halflings-regular.svg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\fonts\glyphicons-halflings-regular.svg
++glyphicons-halflings-regular.ttf
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\fonts\glyphicons-halflings-regular.ttf
++glyphicons-halflings-regular.woff
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\fonts\glyphicons-halflings-regular.woff
++vue.cjs.js
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\vue\vue.cjs.js
++vue.esm-browser.js
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\vue\vue.esm-browser.js
++vue.esm-bundler.js
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\vue\vue.esm-bundler.js
++vue.global.js
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\vue\vue.global.js
++vue.runtime.esm-browser.js
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\vue\vue.runtime.esm-browser.js
++vue.runtime.esm-bundler.js
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\vue\vue.runtime.esm-bundler.js
++vue.runtime.global.js
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\vue\vue.runtime.global.js
++J_YYMS_Setup.exe
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\download\速拓\j_files\j_yyms_setup.exe
++Thumbs.db
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\banks\thumbs.db
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\case\thumbs.db
++工行.gif
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\banks\工行.gif
++工行.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\banks\工行.jpg
++建行.gif
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\banks\建行.gif
++建行.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\banks\建行.jpg
++交通银行.gif
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\banks\交通银行.gif
++交通银行.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\banks\交通银行.jpg
++农行.gif
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\banks\农行.gif
++农行.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\banks\农行.jpg
++微信支付.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\banks\微信支付.jpg
++微信支付.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\banks\微信支付.png
++微信支付qrcode-芬.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\banks\微信支付qrcode-芬.jpg
++微信支付qrcode-军.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\banks\微信支付qrcode-军.jpg
++邮政.gif
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\banks\邮政.gif
++邮政.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\banks\邮政.jpg
++招商银行.gif
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\banks\招商银行.gif
++招商银行.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\banks\招商银行.jpg
++支付宝.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\banks\支付宝.jpg
++支付宝.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\banks\支付宝.png
++支付宝qrcode-芬.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\banks\支付宝qrcode-芬.jpg
++支付宝qrcode-军.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\banks\支付宝qrcode-军.jpg
++about-1.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\carousel\about-1.jpg
++agent-1.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\carousel\agent-1.jpg
++agent-2.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\carousel\agent-2.jpg
++agent-3.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\carousel\agent-3.jpg
++agent-4.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\carousel\agent-4.jpg
++case-1.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\carousel\case-1.jpg
++case-2.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\carousel\case-2.png
++cource-1.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\carousel\cource-1.jpg
++cource-2.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\carousel\cource-2.jpg
++cource-3.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\carousel\cource-3.jpg
++index-1.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\carousel\index-1.jpg
++index-11.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\carousel\index-11.jpg
++index-12.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\carousel\index-12.jpg
++index-13.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\carousel\index-13.jpg
++index-14.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\carousel\index-14.jpg
++index-15.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\carousel\index-15.jpg
++index-2.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\carousel\index-2.jpg
++index-3.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\carousel\index-3.jpg
++index-4.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\carousel\index-4.jpg
++index-5.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\carousel\index-5.jpg
++product-1.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\carousel\product-1.jpg
++product-2.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\carousel\product-2.jpg
++product-3.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\carousel\product-3.jpg
++product-大任-1.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\carousel\product-大任-1.jpg
++product-精诚-1.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\carousel\product-精诚-1.jpg
++product-速拓-1.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\carousel\product-速拓-1.jpg
++product-速腾-1.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\carousel\product-速腾-1.jpg
++purchase-1.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\carousel\purchase-1.jpg
++purchase-2.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\carousel\purchase-2.jpg
++service-1.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\carousel\service-1.jpg
++service-2.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\carousel\service-2.jpg
++service-3.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\carousel\service-3.jpg
++361c.gif
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\case\361c.gif
++anta.gif
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\case\anta.gif
++goldlion.gif
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\case\goldlion.gif
++hqt.gif
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\case\hqt.gif
++kappa.gif
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\case\kappa.gif
++lining.gif
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\case\lining.gif
++montagut.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\case\montagut.png
++playboy.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\case\playboy.jpg
++puma.gif
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\case\puma.gif
++qipai.gif
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\case\qipai.gif
++把因医疗.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\case\把因医疗.png
++宝芝林.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\case\宝芝林.png
++二天堂.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\case\二天堂.png
++好药师.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\case\好药师.png
++老百姓.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\case\老百姓.png
++路加医疗.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\case\路加医疗.png
++同济堂.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\case\同济堂.png
++益丰.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\case\益丰.png
++永安堂.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\case\永安堂.png
++鱼跃医疗.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\case\鱼跃医疗.png
++online-user.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\icon\online-user.png
++qq.gif
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\icon\qq.gif
++qq-talk.gif
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\icon\qq-talk.gif
++usage-rate.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\icon\usage-rate.png
++user-register.png
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\icon\user-register.png
++person1.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\person\person1.jpg
++person2.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\person\person2.jpg
++person3.jpg
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\img\ow\person\person3.jpg
++axios.cjs.map
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\axios\browser\axios.cjs.map
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\axios\node\axios.cjs.map
++axios.js.map
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\axios\axios.js.map
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\axios\esm\axios.js.map
++axios.min.js
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\axios\axios.min.js
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\axios\esm\axios.min.js
++bootstrap.min.css
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\bootstrap\bootstrap.min.css
++locale
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\element-plus\dist\locale\
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\element-plus\es\locale\
++index.css
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\element-plus\dist\index.css
++index.full.min.js
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\element-plus\dist\index.full.min.js
++vue.cjs.min.js
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\vue\vue.cjs.min.js
++vue.cjs.prod.js
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\vue\vue.cjs.prod.js
++vue.esm-browser.min.js
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\vue\vue.esm-browser.min.js
++vue.esm-browser.prod.js
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\vue\vue.esm-browser.prod.js
++vue.esm-bundler.min.js
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\vue\vue.esm-bundler.min.js
++vue.global.min.js
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\vue\vue.global.min.js
++vue.global.prod.js
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\vue\vue.global.prod.js
++vue.runtime.esm-browser.min.js
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\vue\vue.runtime.esm-browser.min.js
++vue.runtime.esm-browser.prod.js
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\vue\vue.runtime.esm-browser.prod.js
++vue.runtime.esm-bundler.min.js
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\vue\vue.runtime.esm-bundler.min.js
++vue.runtime.global.min.js
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\vue\vue.runtime.global.min.js
++vue.runtime.global.prod.js
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\vue\vue.runtime.global.prod.js
++axios.min.js.map
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\axios\axios.min.js.map
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\axios\esm\axios.min.js.map
++zh-cn.min.mjs
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\element-plus\dist\locale\zh-cn.min.mjs
++index.full.min.js.map
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\element-plus\dist\index.full.min.js.map
++lang
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\element-plus\es\locale\lang\
++vue.cjs.prod.min.js
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\vue\vue.cjs.prod.min.js
++vue.esm-browser.prod.min.js
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\vue\vue.esm-browser.prod.min.js
++vue.global.prod.min.js
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\vue\vue.global.prod.min.js
++vue.runtime.esm-browser.prod.min.js
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\vue\vue.runtime.esm-browser.prod.min.js
++vue.runtime.global.prod.min.js
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\vue\vue.runtime.global.prod.min.js
++zh-cn.min.mjs.map
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\element-plus\dist\locale\zh-cn.min.mjs.map
++zh-cn.mjs
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\element-plus\es\locale\lang\zh-cn.mjs
++zh-cn.mjs.map
i:{f41f40d3-fd6b-495b-9745-3096acd10751}:e:\cursortest\darenmis20250624(1)\darenmain\staticfiles\lib\element-plus\es\locale\lang\zh-cn.mjs.map
