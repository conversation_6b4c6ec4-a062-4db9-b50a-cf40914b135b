﻿using DarenCore.Extension;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DarenCore.Models.BaseModel
{
    public class BaseBillMainModel : BaseParentModel
    {
        public BaseBillMainModel()
        {
            bill_status = 0;
            print_times = 0;
        }

        [DisplayName("单据编号")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string bill_no { get; set; }

        [DisplayName("单据日期")]
        public DateTime? bill_date { get; set; }

        [DisplayName("审核日期")]
        public DateTime? audit_date { get; set; }

        [DisplayName("单据状态")]
        public int bill_status { get; set; }

        [DisplayName("门店")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string shop_id { get; set; }

        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string shop_name { get; set; }

        [DisplayName("部门")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string department_id { get; set; }

        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string department_name { get; set; }

        [DisplayName("仓库")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string warehouse_id { get; set; }

        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string warehouse_name { get; set; }

        [DisplayName("经办人")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string handler_id { get; set; }

        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string handler_code { get; set; }

        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string handler_name { get; set; }

        [DisplayName("制单人")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string maker_id { get; set; }

        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string maker_name { get; set; }

        [DisplayName("审核人")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string? auditor_id { get; set; }

        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string? auditor_name { get; set; }

        [DisplayName("打印次数")]
        public int print_times { get; set; }

        [StringLength(255)]
        [DisplayName("摘要")]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string abstracts { get; set; }

        [DisplayName("备注")]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string remark { get; set; }

        //以下为自定义字段

        [DisplayName("单据日期")]
        [NotMapped]
        public string bill_date_short
        {
            get
            {
                return bill_date.ToDateStr();
            }
            set
            {
                bill_date = (value + " " + bill_date.ToTimeStr()).ToDateTime();
            }
        }

        [DisplayName("开始日期")]
        [NotMapped]
        public string bill_date1 { get; set; }

        [DisplayName("结束日期")]
        [NotMapped]
        public string bill_date2 { get; set; }

        [DisplayName("商品ID")]
        [NotMapped]
        public string product_id { get; set; }

        [DisplayName("商品分类ID")]
        [NotMapped]
        public string category_id { get; set; }

    }

}