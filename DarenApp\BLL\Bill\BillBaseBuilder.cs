﻿using DarenApp.BLL.Dic;
using DarenApp.Models.Bill.SalesOut;
using DarenCore.Common;
using DarenCore.Extension;
using DarenCore.Interface;
using DarenCore.Models;

namespace DarenApp.BLL.Bill
{
    public abstract class BillBaseBuilder(IWebAppContext ctx) : IBillBuilder
    {
        protected readonly IWebAppContext ctx = ctx;
        protected readonly OnlineUser user = ctx.User;
        protected readonly ErpType erpType = ctx.DbConfig.ErpType;
        protected readonly ParamConfig paramConfig = ctx.ParamOption.ParamConfig;
        protected readonly DigitsSetting digitsSetting = ctx.ParamOption.DigitsSetting;

        private SupplierBLL? _supplierBLL = null;
        public SupplierBLL supplierBLL
        {
            get
            {
                _supplierBLL ??= new(ctx);
                return _supplierBLL;
            }
        }

        private CustomerBLL? _customerBLL = null;
        public CustomerBLL customerBLL
        {
            get
            {
                _customerBLL ??= new(ctx);
                return _customerBLL;
            }
        }

        private BillSnBLL? _billSnBLL = null;
        public BillSnBLL billSnBLL
        {
            get
            {
                _billSnBLL ??= new(ctx);
                return _billSnBLL;
            }
        }


        /// <summary>
        /// 基本信息校验
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public virtual Task<(bool, string?)> CheckMainAsync(BillBuilderType type)
        {
            return Task.FromResult<(bool, string?)>((true, null));
        }

        //基本信息初始化
        public virtual void InitMain()
        {

        }

         
        /// <summary>
        /// 构建基本信息
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public async Task<Result> BuildMainAsync(BillBuilderType type)
        {
            var (ok, msg) = await CheckMainAsync(type);
            if (ok)
            {
                InitMain();
                return Result.Success();
            }
            return Result.Fail(msg);
        }

        /// <summary>
        /// 获取商品行数
        /// </summary>
        /// <returns></returns>
        public virtual int GetPoductCount()
        {
            return 0;
        }

        /// <summary>
        /// 商品信息校验
        /// </summary>
        /// <param name="i"></param>
        /// <param name="type"></param>
        /// <returns>第一个参数值：0正常，1删除，2错误描述，3缺少属性</returns>
        public virtual (int, string?, string?) CheckProduct(int i, BillBuilderType type)
        {
            return (0, null, null);
        }

        /// <summary>
        /// 商品信息初始化
        /// </summary>
        /// <param name="i"></param>
        public virtual void InitProduct(int i)
        {

        }

        /// <summary>
        /// 商品信息计算
        /// </summary>
        /// <param name="i"></param>
        public virtual void CalculateProduct(int i)
        {

        }

        //构建商品信息
        public Result BuildProduct(BillBuilderType type)
        {
            int count =0;
            for (int i = 0; i < GetPoductCount(); i++)
            {
                var (flag, describe, property) = CheckProduct(i, type);
                if (flag == 1)
                {
                    i--;
                    continue;
                }

                if (flag == 2)
                {
                    return Result.Fail($"第{i + 1}行的{describe}", new { index = i + 1 });
                }
                
                if (flag == 3)
                {
                    return Result.Fail($"第{i + 1}行的[{property}]还没有填写！", new { index = i + 1 });
                }

                count++;
                InitProduct(i);
                CalculateProduct(i);
            }
            if (type == BillBuilderType.AUDIT && count == 0)
            {
                return Result.Fail("请至少添加1个商品！");
            }
            return Result.Success();
        }

        /// <summary>
        /// 获取结算信息行数
        /// </summary>
        /// <returns></returns>
        public virtual int GetSettlementCount()
        {
            return 0;
        }

        /// <summary>
        /// 结算信息校验
        /// </summary>
        /// <param name="i"></param>
        /// <returns>第一个参数值：0正常，1删除，2错误描述，3缺少属性</returns>
        public virtual (int, string?, string?) CheckSettlement(int i)
        {
            return (0, null, null);
        }

        /// <summary>
        /// 结算信息初始化
        /// </summary>
        /// <param name="i"></param>
        public virtual void InitSettlement(int i)
        {

        }

        /// <summary>
        /// 结算信息计算
        /// </summary>
        /// <param name="i"></param>
        public virtual void CalculateSettlement(int i)
        {

        }

        /// <summary>
        /// 构建结算信息
        /// </summary>
        /// <returns></returns>
        public Result BuildSettlement(BillBuilderType type)
        {
            int count = 0;
            for (int i = 0; i < GetSettlementCount(); i++)
            {
                var (flag, describe, property) = CheckSettlement(i);
                if (flag == 1)
                {
                    i--;
                    continue;
                }

                if (flag == 2)
                {
                    return Result.Fail($"第{i + 1}行的{describe}", new { index = i + 1 });
                }

                if (flag == 3)
                {
                    return Result.Fail($"第{i + 1}行的[{property}]还没有填写！", new { index = i + 1 });
                }

                count++;

                InitSettlement(i);
                CalculateSettlement(i);
            }
            if (type == BillBuilderType.AUDIT && count == 0)
            {
                return Result.Fail("请至少选择1个结算方式！");
            }
            return Result.Success();
        }

         /// <summary>
        /// 构建保存事件
        /// </summary>
        /// <param name="flag"></param>
        /// <returns></returns>
        protected virtual Task<Result> BuildSaveAsync(bool flag)
        {
            return Task.FromResult(Result.Success());
        }

        /// <summary>
        /// 构建审核事件
        /// </summary>
        /// <returns></returns>
        protected virtual Task<Result> BuildAuditAsync()
        {
            return Task.FromResult(Result.Success());
        }

        /// <summary>
        /// 构建反审事件
        /// </summary>
        /// <returns></returns>
        protected virtual Task<Result> BuildCancelAsync()
        {
            return Task.FromResult(Result.Success());
        }

        /// <summary>
        /// 保存单据
        /// </summary>
        /// <returns></returns>
        public async Task<Result> SaveBillAsync()
        {
            Result r;
            try
            {
                await ctx.DbContext.BeginTransAsync();
                r = await BuildSaveAsync(true);
                if (r.IsSuccess)
                {
                    await ctx.DbContext.CommitAsync();
                }
                else
                {
                    await ctx.DbContext.RollbackAsync();
                }
            }
            catch (Exception ex)
            {
                r = Result.Fail(ex);
            }
            return r;
        }

        /// <summary>
        /// 审核单据
        /// </summary>
        /// <returns></returns>
        public async Task<Result> AuditBillAsync()
        {
            Result r;
            try
            {
                await ctx.DbContext.BeginTransAsync();
                r = await BuildSaveAsync(false);
                if (r.IsSuccess)
                {
                    r = await BuildAuditAsync();
                    if (r.IsSuccess)
                    {
                        await ctx.DbContext.CommitAsync();
                    }
                    else
                    {
                        await ctx.DbContext.RollbackAsync();
                    }
                }
                else
                {
                    await ctx.DbContext.RollbackAsync();
                }
            }
            catch (Exception ex)
            {
                r = Result.Fail(ex);
            }
            return r;
        }

        /// <summary>
        /// 取消审核单据
        /// </summary>
        /// <returns></returns>
        public async Task<Result> CancelBillAsync()
        {
            Result r;
            try
            {
                await ctx.DbContext.BeginTransAsync();
                r = await BuildCancelAsync();
                if (r.IsSuccess)
                {
                    await ctx.DbContext.CommitAsync();
                }
                else
                {
                    await ctx.DbContext.RollbackAsync();
                }
            }
            catch (Exception ex)
            {
                r = Result.Fail(ex);
            }

            return r;
        }

        /// <summary>
        /// 获取往来单位往来余额
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        protected async Task<object?> GetCurrentBalance(string id)
        {
            var (m, ex) = await customerBLL.GetModelByIdAsync(id);
            if (m == null || ex != null)
            {
                return null;
            }
            return new
            {
                m.advance_balance,
                m.prepaid_balance,
                m.receivable_balance,
                m.payable_balance
            };
        }

        public static string GetAbstracts(string billName, string? item)
        {
            if (string.IsNullOrEmpty(item))
            {
                return billName;
            }
            return $"{SalesOutBill.bill_name}[{item.CutString(40)}]";
        }
    }


}
