﻿using Climber.Model;
using DarenCore.Common;

namespace DarenApp.BLL.Bill.SalesReturn.MainChecker
{
    public class CheckerFactory
    {
        public static IBillMainChecker<SalesReturnMain> Get(ErpType erpType)
        {
            return erpType switch
            {
                ErpType.DRUG or ErpType.INSTRUMENT => new DrugChecker(),
                _ => new CommonChecker(),
            };
        }
    }
}
