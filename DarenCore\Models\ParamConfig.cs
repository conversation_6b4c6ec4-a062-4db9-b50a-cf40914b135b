﻿using DarenCore.Models.BaseModel;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DarenCore.Models
{
    //系统参数配置

    public class ParamConfig : BaseIdModel
    {
        public ParamConfig()
        {
            bonus2cash_rate = 1;
            is_voucher_bonus = 0;
            is_negative_stock = 0;
            is_enable_kitchen = 1;
        }

        [DisplayName("公司名称")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string company_name { get; set; }

        [DisplayName("公司简称")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        [Required]
        public string company_short { get; set; }

        [DisplayName("管理员密码")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        [Column("AdminPassword")]
        public string admin_password { get; set; }

        [DisplayName("默认供货商")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string default_supplier_id { get; set; }

        [DisplayName("默认客户")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string default_customer_id { get; set; }

        [DisplayName("进价跟踪方式")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string purchase_price_mode { get; set; }

        [DisplayName("售价跟踪方式")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string sales_price_mode { get; set; }

        [DisplayName("税率")]
        public decimal tax_rate { get; set; }

        [DisplayName("积分比例")]
        public decimal bonus_rate { get; set; }

        [DisplayName("积分兑现金比例")]
        public decimal bonus2cash_rate { get; set; }

        [DisplayName("代金券是否积分")]
        public int is_voucher_bonus { get; set; }

        [DisplayName("负库存是否允许")]
        public int is_negative_stock { get; set; }

        [DisplayName("根据员工部门限制其在业务单据中是否显示")]
        public int is_limit_employee { get; set; }

        [DisplayName("厨部功能是否启用")]
        public int is_enable_kitchen { get; set; }

        [DisplayName("全场打折-是否启用")]
        public int fd_is_enable { get; set; }

        [DisplayName("全场打折-是否仅适用会员")]
        public int fd_is_member_only { get; set; }

        [DisplayName("全场打折-折扣率")]
        public decimal fd_discount { get; set; }

        [DisplayName("全场打折-积分倍数")]
        public decimal fd_bonus_multiple { get; set; }  //erp中是decimal，因此不能用int

        [DisplayName("公告")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        [Column("MSG")]
        public string notice { get; set; }
    }

}