<?xml version="1.0"?>
<doc>
    <assembly>
        <name>DarenMain</name>
    </assembly>
    <members>
        <member name="T:DarenMain.WebApp">
            <summary>
            基础WebApp
            </summary>
            <param name="options"></param>
        </member>
        <member name="M:DarenMain.WebApp.#ctor(DarenCore.Common.AppOptions)">
            <summary>
            基础WebApp
            </summary>
            <param name="options"></param>
        </member>
        <member name="T:DarenMain.WebAppMaster">
            <summary>
            主WebApp
            </summary>
            <param name="options"></param>
        </member>
        <member name="M:DarenMain.WebAppMaster.#ctor(DarenCore.Common.AppOptions)">
            <summary>
            主WebApp
            </summary>
            <param name="options"></param>
        </member>
    </members>
</doc>
