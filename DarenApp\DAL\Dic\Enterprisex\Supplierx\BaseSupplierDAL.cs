﻿using DarenApp.Models.Dic;
using DarenCore.Extension;

namespace DarenApp.DAL.Dic.Enterprisex.Supplierx
{
    public abstract class BaseSupplierDAL : EnterpriseDAL<Supplier>, ISupplierDAL
    {
        public override string GetLimit(Supplier m)
        {
            var sb = CreateStatementBuilder();

            sb.AddLikeBoth("BM", m.code);
            sb.AddLikeBoth("MC", m.name);
            sb.AddLikeBoth("DZ", m.address);
            sb.AddLikeBoth("DH", m.telephone);
            sb.AddLikeBoth("SJ", m.mobile);
            sb.AddLikeBoth("JP", m.spell);

            if (!m.search.IsNullOrEmpty())
            {
                sb.AddLikeBoth("BM", m.search, null, "and (");
                sb.AddLikeBoth("MC", m.search, "or");
                sb.AddLikeBoth("DZ", m.search, "or");
                sb.AddLikeBoth("DH", m.search, "or");
                sb.AddLikeBoth("SJ", m.search, "or");
                sb.AddLikeBoth("JP", m.search, "or", null, ")");
            }

            sb.Add("and DWLX=1");
            sb.AddGroup("LB", m.category_id);
            sb.AddErpDepartmentAll("LLBM", m.department_id, user);
            sb.AddBoolStr("QY_FLAG", m.is_use);

            return sb.ToString();
        }

    }
}
