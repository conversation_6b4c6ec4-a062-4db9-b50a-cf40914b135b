{"Version": 1, "WorkspaceRootPath": "E:\\cursortest\\dear\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{0A2E48CE-4E69-4E53-B703-5660B3621105}|DarenWeb\\DarenWeb.csproj|e:\\cursortest\\dear\\darenweb\\views\\ow\\service.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{0A2E48CE-4E69-4E53-B703-5660B3621105}|DarenWeb\\DarenWeb.csproj|solutionrelative:darenweb\\views\\ow\\service.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{F41F40D3-FD6B-495B-9745-3096ACD10751}|DarenMain\\DarenMain.csproj|e:\\cursortest\\dear\\darenmain\\option\\config.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{F41F40D3-FD6B-495B-9745-3096ACD10751}|DarenMain\\DarenMain.csproj|solutionrelative:daren<PERSON>in\\option\\config.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{F41F40D3-FD6B-495B-9745-3096ACD10751}|DarenMain\\DarenMain.csproj|e:\\cursortest\\dear\\darenmain\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{F41F40D3-FD6B-495B-9745-3096ACD10751}|DarenMain\\DarenMain.csproj|solutionrelative:darenmain\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{8AC92D3B-10AD-44F0-9CCC-0443E878AFE7}|DarenApi\\DarenApi.csproj|e:\\cursortest\\dear\\darenapi\\darenapi.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{8AC92D3B-10AD-44F0-9CCC-0443E878AFE7}|DarenApi\\DarenApi.csproj|solutionrelative:darenapi\\darenapi.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\MicrosoftVisualStudio\\Enterprise\\MSBuild\\Current\\Bin\\amd64\\Microsoft.Common.CurrentVersion.targets||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{63412004-A5AB-4DB4-ABDC-FE7D0A17A61B}|DarenShare\\DarenShare.csproj|e:\\cursortest\\dear\\darenshare\\dbcontext\\webdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{63412004-A5AB-4DB4-ABDC-FE7D0A17A61B}|DarenShare\\DarenShare.csproj|solutionrelative:darenshare\\dbcontext\\webdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6787278C-1204-49F7-A3A4-E088656ACB21}|DarenCore\\DarenCore.csproj|e:\\cursortest\\dear\\darencore\\utils\\jsonutils.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6787278C-1204-49F7-A3A4-E088656ACB21}|DarenCore\\DarenCore.csproj|solutionrelative:darencore\\utils\\jsonutils.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F41F40D3-FD6B-495B-9745-3096ACD10751}|DarenMain\\DarenMain.csproj|e:\\cursortest\\dear\\darenmain\\webapp.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F41F40D3-FD6B-495B-9745-3096ACD10751}|DarenMain\\DarenMain.csproj|solutionrelative:darenmain\\webapp.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Bookmark", "Name": "ST:129:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "Service.cshtml", "DocumentMoniker": "E:\\cursortest\\dear\\DarenWeb\\Views\\OW\\Service.cshtml", "RelativeDocumentMoniker": "DarenWeb\\Views\\OW\\Service.cshtml", "ToolTip": "E:\\cursortest\\dear\\DarenWeb\\Views\\OW\\Service.cshtml", "RelativeToolTip": "DarenWeb\\Views\\OW\\Service.cshtml", "ViewState": "AgIAALEAAAAAAAAAAADwv2QAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-04T10:56:38.939Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "Config.json", "DocumentMoniker": "E:\\cursortest\\dear\\DarenMain\\Option\\Config.json", "RelativeDocumentMoniker": "DarenMain\\Option\\Config.json", "ToolTip": "E:\\cursortest\\dear\\DarenMain\\Option\\Config.json", "RelativeToolTip": "DarenMain\\Option\\Config.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-08-04T10:42:45.811Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "launchSettings.json", "DocumentMoniker": "E:\\cursortest\\dear\\DarenMain\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "DarenMain\\Properties\\launchSettings.json", "ToolTip": "E:\\cursortest\\dear\\DarenMain\\Properties\\launchSettings.json", "RelativeToolTip": "DarenMain\\Properties\\launchSettings.json", "ViewState": "AgIAAAAAAAAAAAAAAADwvwcAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-08-04T10:41:58.739Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "Microsoft.Common.CurrentVersion.targets", "DocumentMoniker": "D:\\MicrosoftVisualStudio\\Enterprise\\MSBuild\\Current\\Bin\\amd64\\Microsoft.Common.CurrentVersion.targets", "ToolTip": "D:\\MicrosoftVisualStudio\\Enterprise\\MSBuild\\Current\\Bin\\amd64\\Microsoft.Common.CurrentVersion.targets", "ViewState": "AgIAANMTAAAAAAAAAAAhwNkTAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003801|", "WhenOpened": "2025-08-04T10:39:38.974Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "WebDbContext.cs", "DocumentMoniker": "E:\\cursortest\\dear\\DarenShare\\DbContext\\WebDbContext.cs", "RelativeDocumentMoniker": "DarenShare\\DbContext\\WebDbContext.cs", "ToolTip": "E:\\cursortest\\dear\\DarenShare\\DbContext\\WebDbContext.cs", "RelativeToolTip": "DarenShare\\DbContext\\WebDbContext.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAADoAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-04T10:36:34.244Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "JsonUtils.cs", "DocumentMoniker": "E:\\cursortest\\dear\\DarenCore\\Utils\\JsonUtils.cs", "RelativeDocumentMoniker": "DarenCore\\Utils\\JsonUtils.cs", "ToolTip": "E:\\cursortest\\dear\\DarenCore\\Utils\\JsonUtils.cs", "RelativeToolTip": "DarenCore\\Utils\\JsonUtils.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-04T10:36:03.056Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "DarenApi.csproj", "DocumentMoniker": "E:\\cursortest\\dear\\DarenApi\\DarenApi.csproj", "RelativeDocumentMoniker": "DarenApi\\DarenApi.csproj", "ToolTip": "E:\\cursortest\\dear\\DarenApi\\DarenApi.csproj", "RelativeToolTip": "DarenApi\\DarenApi.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAABsAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-08-04T10:32:42.086Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "WebApp.cs", "DocumentMoniker": "E:\\cursortest\\dear\\DarenMain\\WebApp.cs", "RelativeDocumentMoniker": "DarenMain\\WebApp.cs", "ToolTip": "E:\\cursortest\\dear\\DarenMain\\WebApp.cs", "RelativeToolTip": "DarenMain\\WebApp.cs", "ViewState": "AgIAACUAAAAAAAAAAAAAwB0AAAA3AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-04T10:29:06.91Z"}]}]}]}