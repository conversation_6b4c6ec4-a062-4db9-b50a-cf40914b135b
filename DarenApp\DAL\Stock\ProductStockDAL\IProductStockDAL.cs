using DarenApp.Models.Stock;
using DarenCore.Common;
using DarenCore.DAL.OldVersion;
using DarenCore.Models;

namespace DarenApp.DAL.Stock.ProductStockDAL
{
    public interface IProductStockDAL : IBaseDAL<ProductStock>
    {

        //Result SaveModel(ProductStock m);

        //bool ExistsModel(ProductStock m);

        Task<Result> UpdateStockAsync(ProductStock m, int sn, bool isIncrease, bool isAudit, ParamOption option);

        Task<ProductStock?> GetModelAsync(ProductStock m);

        //ProductStock GetModel8Id(string id);

        //ProductStock GetModel8SerialNo(string department_id, string product_id, string serial_no);

        //ProductStock GetModel8Department(string department_id, string warehouse_id, string product_id);

        //ProductStock GetModel8Shop(string shop_id, string warehouse_id, string product_id);

        //string GetPage(ProductStock m);

        //List<ProductStock> GetList(string department_id, string warehouse_id, string product_id);

        //List<ProductStock> GetListForBatchNoChoice(string department_id, string warehouse_id, string product_id, string batch_no);

        //List<ProductStock> GetListForSerialNoChoice(string department_id, string warehouse_id, string product_id, string batch_no, string color_id);

        //List<ProductStock> GetListForSizeChoice(string department_id, string warehouse_id, string product_id, string group_id, string color_id);

        //List<ProductStock> GetListForTopSync(string department_id, string warehouse_id, string[] product_ids);

        //List<string> GetProductIdsForStoreSync(string department_id, string warehouse_id);
    }

}
