﻿using DarenApp.Models.Dic;
using DarenCore.Common;
using DarenCore.DAL.OldVersion;

namespace DarenApp.DAL.Dic.Departmentx
{
    public class BaseDepartmentDAL : BaseDicDAL<Department>, IDepartmentDAL
    {
        public override string? GetTableName()
        {
            return "BM_BM";
        }

        private string GetLimit(Department m, int limitPower)
        {
            var sb = CreateStatementBuilder();
            sb.AddStr("BM", m.code);
            sb.AddStr("MC", m.name);
            if (!string.IsNullOrEmpty(m.search))
            {
                sb.AddLikeBoth("BM", m.search, null, "and (");
                sb.AddLikeBoth("MC", m.search, "or");
                sb.AddLikeBoth("JP", m.search, "or", null, ")");
            }
            sb.AddDic("FL", "*");
            sb.AddBoolStr("QY_FLAG", m.is_use);
            if (limitPower == 1)
            {
                sb.Append(ErpUserPower.GetPowerDepartment(user, "BM"));
            }
            return sb.ToString();
        }

        public Task<Department?> GetModelByOuterCodeAsync(string outer_code)
        {
            return dao.GetModelByLimitAsync($"DWBH='{outer_code}'");
        }

        public Task<List<Department>> GetListAsync(Department m, string? orderby, int limitPower)
        {
            return dao.GetListByLimitAsync(GetLimit(m, limitPower), orderby ?? "BM");
        }

        public Task<string> GetPageJsonAsync(Department m, int pageIndex, int pageSize, string? orderby, int limitPower)
        {
            return dao.GetPageJsonAsync(pageIndex, pageSize, GetLimit(m, limitPower), orderby ?? "BM");
        }
    }
}
