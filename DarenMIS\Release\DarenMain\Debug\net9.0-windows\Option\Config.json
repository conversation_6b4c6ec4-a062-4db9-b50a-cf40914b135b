﻿{
  "AppName": "大任官网",
  "AppUrls": [ "http://localhost:8099" ],
  "ReverseProxy": {
    "IsUse": 1,
    "HostMap": [
      {
        "From": "localhost",
        "To": ":81",
        "IsUse": 0
      },
      {
        "From": "www.hbst666.com",
        "To": ":81",
        "IsUse": 1
      },
      {
        "From": "www.hbst123.com",
        "To": ":81",
        "IsUse": 1
      },
      {
        "From": "www.hbst999.com",
        "To": ":82",
        "IsUse": 1
      },
      {
        "From": "www.0710999.com",
        "To": ":83",
        "IsUse": 1
      },
      {
        "#": "旧版客来宝-视图",
        "From": "klb.daren777.com,klb.hbst999.com,klb.hbst666.com,klb.hbst123.com,klb.0710999.com",
        "To": ":8090",
        "IsUse": 1
      },
      {
        "#": "旧版客来宝-服务",
        "From": "kli.daren777.com,kli.hbst999.com,kli.hbst666.com,kli.hbst123.com,kli.0710999.com",
        "To": ":8080",
        "IsUse": 1
      },
      {
        "#": "新版客来宝-视图",
        "From": "clb.daren777.com,clb.hbst999.com,clb.hbst666.com,clb.hbst123.com,clb.0710999.com",
        "To": ":9119",
        "IsUse": 1
      },
      {
        "#": "新版客来宝-服务",
        "From": "cli.daren777.com,cli.hbst999.com,cli.hbst666.com,cli.hbst123.com,cli.0710999.com",
        "To": ":9119",
        "IsUse": 1
      }
    ]
  },
  "BackupConfig": {
    "IsUse": 1,
    "HostId": "103",
    "ExecuteTime": "1:00", //1:00
    "DeleteDays": 7
  },
  "DbConfig": {
    "ServerIp": "*************", //*************|**************
    "ServerPort": "64333",
    "DbType": "0", //0-SqlServer,1-MySql
    "DbName": "daren_web",
    "DbUser": "daren_test_yy_adv",
    "DbPassword": "Hbst123"
  },
  "LocalDbConfig": {
    "ServerIp": "*************", //*************|**************
    "ServerPort": "64333",
    "DbType": "0", //0-SqlServer,1-MySql
    "DbName": "daren_web",
    "DbUser": "daren_test_yy_adv",
    "DbPassword": "Hbst123"
  },
  "JwtConfig": {
    "PassRules": {
      "ow": [ "*" ],
      "api/*": [ "*" ],
      "web/home": [ "login", "error" ],
      "app/home": [ "login", "error" ]
    },
    "Audience": "customer",
    "Issuer": "daren",
    "SignKey": "c75454de355641e0bbd3b11e5e1b5ba4",
    "ExpiresMinutes": 120,
    "ClockSkewMinutes": 10
  },
  "IsDevelopment": 1,
  "ApiToken": "37lmU4ltLkwkYaEo1zI4bs8BPg6DGBxSmSgBdN_hRjg"
}