{"Version": 1, "WorkspaceRootPath": "E:\\cursortest\\dear\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{6787278C-1204-49F7-A3A4-E088656ACB21}|DarenCore\\DarenCore.csproj|e:\\cursortest\\dear\\darencore\\abstract\\abstractwebapp.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6787278C-1204-49F7-A3A4-E088656ACB21}|DarenCore\\DarenCore.csproj|solutionrelative:darencore\\abstract\\abstractwebapp.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{CD008736-229D-457D-9ECD-E4F9C918F266}|DarenApp\\DarenApp.csproj|e:\\cursortest\\dear\\darenapp\\common\\webapp.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{CD008736-229D-457D-9ECD-E4F9C918F266}|DarenApp\\DarenApp.csproj|solutionrelative:darenapp\\common\\webapp.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{CD008736-229D-457D-9ECD-E4F9C918F266}|DarenApp\\DarenApp.csproj|e:\\cursortest\\dear\\darenapp\\main.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{CD008736-229D-457D-9ECD-E4F9C918F266}|DarenApp\\DarenApp.csproj|solutionrelative:darenapp\\main.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6787278C-1204-49F7-A3A4-E088656ACB21}|DarenCore\\DarenCore.csproj|e:\\cursortest\\dear\\darencore\\middlewares\\reverseproxymiddleware.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6787278C-1204-49F7-A3A4-E088656ACB21}|DarenCore\\DarenCore.csproj|solutionrelative:darencore\\middlewares\\reverseproxymiddleware.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "AbstractWebApp.cs", "DocumentMoniker": "E:\\cursortest\\dear\\DarenCore\\Abstract\\AbstractWebApp.cs", "RelativeDocumentMoniker": "DarenCore\\Abstract\\AbstractWebApp.cs", "ToolTip": "E:\\cursortest\\dear\\DarenCore\\Abstract\\AbstractWebApp.cs", "RelativeToolTip": "DarenCore\\Abstract\\AbstractWebApp.cs", "ViewState": "AgIAAKcAAAAAAAAAAAAcwFkAAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-04T11:13:02.37Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "WebApp.cs", "DocumentMoniker": "E:\\cursortest\\dear\\DarenApp\\Common\\WebApp.cs", "RelativeDocumentMoniker": "DarenApp\\Common\\WebApp.cs", "ToolTip": "E:\\cursortest\\dear\\DarenApp\\Common\\WebApp.cs", "RelativeToolTip": "DarenApp\\Common\\WebApp.cs", "ViewState": "AgIAAEEAAAAAAAAAAAAYwFUAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-04T11:12:46.467Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "Main.cs", "DocumentMoniker": "E:\\cursortest\\dear\\DarenApp\\Main.cs", "RelativeDocumentMoniker": "DarenApp\\Main.cs", "ToolTip": "E:\\cursortest\\dear\\DarenApp\\Main.cs", "RelativeToolTip": "DarenApp\\Main.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAMwBIAAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-04T11:21:59.827Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "ReverseProxyMiddleware.cs", "DocumentMoniker": "E:\\cursortest\\dear\\DarenCore\\Middlewares\\ReverseProxyMiddleware.cs", "RelativeDocumentMoniker": "DarenCore\\Middlewares\\ReverseProxyMiddleware.cs", "ToolTip": "E:\\cursortest\\dear\\DarenCore\\Middlewares\\ReverseProxyMiddleware.cs", "RelativeToolTip": "DarenCore\\Middlewares\\ReverseProxyMiddleware.cs", "ViewState": "AgIAAFAAAAAAAAAAAAAYwFEAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-04T11:20:09.029Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:129:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}]}]}]}