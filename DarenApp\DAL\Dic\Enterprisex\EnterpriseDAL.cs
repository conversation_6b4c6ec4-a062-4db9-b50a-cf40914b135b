﻿using DarenApp.Models.Dic;
using DarenCore.DAL.OldVersion;

namespace DarenApp.DAL.Dic.Enterprisex
{
    public class EnterpriseDAL<T> : BaseDicDAL<T>, IEnterpriseDAL<T> where T : class
    {
        public override string? GetTableName()
        {
            return "BM_WLDW";
        }

        public Task<int> InitCurrentBalanceAsync(Enterprise m)
        {
            var up = CreateUpdateBuilder();
            up.AddItemDecimal("YUSYE", m.advance_balance);
            up.AddItemDecimal("YINSYE", m.receivable_balance);
            up.AddItemDecimal("YUFYE", m.prepaid_balance);
            up.AddItemDecimal("YINFYE", m.payable_balance);
            up.AddItemDecimal("WLYE", m.current_balance);
            up.AddWhereItemString("BM", m.id);
            return dao.UpdateAsync(up);
        }

        public async Task<(int, Exception?)> UpdateCurrentBalanceAsync(string id, decimal amount, string type, bool isIncrease)
        {
            if (!"YINSYE,YUSYE,YINFYE,YUFYE".Contains(type))
            {
                return (0, new Exception("往来余额类型不正确"));
            }

            var up = CreateUpdateBuilder();
            if (isIncrease)
            {
                up.AddCalculateItem("WLYE=ISNULL(WLYE,0)+" + amount);
                up.AddCalculateItem(type + "=ISNULL(" + type + ",0)+" + amount);
            }
            else
            {
                up.AddCalculateItem("WLYE=ISNULL(WLYE,0)-" + amount);
                up.AddCalculateItem(type + "=ISNULL(" + type + ",0)-" + amount);
            }
            up.AddWhereItemString("BM", id);
            var n = await dao.UpdateAsync(up);
            return (n, null);
        }

        public Task<int> DeleteByIdAsync(string id, int type)
        {
            var wb = CreateWhereBuilder();
            wb.AddItemInt("DWLX", type);
            wb.AddItemString("BM", id);
            return dao.DeleteByWhereBuilderAsync(wb);
        }

        public Task<int> DeleteByIdsAsync(string ids, int type)
        {
            var wb = CreateWhereBuilder();
            wb.AddItemInt("DWLX", type);
            wb.AddInStatement("BM", ids);
            return dao.DeleteByWhereBuilderAsync(wb);
        }
    }
}
