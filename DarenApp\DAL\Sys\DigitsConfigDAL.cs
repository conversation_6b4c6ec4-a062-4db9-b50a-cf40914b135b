﻿using DarenCore.DAL.OldVersion;
using DarenCore.Models;

namespace DarenApp.DAL.Sys
{
    public class DigitsConfigDAL : BaseDAL<DigitsConfig>
    {
        public override string? GetTableName()
        {
            return "GG_TB_XS";
        }

        public Task<List<DigitsConfig>> GetListAsync()
        {
            return dao.GetListAsync();
        }

        public override string GetTableFields()
        {
            return FieldsBuilder.fields;
        }

        private class FieldsBuilder
        {
            internal static readonly string fields = "XH sn,XM item,XSW digits,FW range";
        }
    }
}