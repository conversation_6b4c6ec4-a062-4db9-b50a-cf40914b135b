﻿using DarenApp.Models.Stock;
using DarenCore.Common;
using DarenCore.DAL.OldVersion;
using DarenCore.Models;

namespace DarenApp.DAL.Stock.ProductStockDAL
{
    public abstract class BaseProductStockDAL : BaseDAL<ProductStock>, IProductStockDAL
    {
        public override string? GetTableName()
        {
            return "KC_KCDTB";
        }

        public virtual Task<bool> ExistsModelAsync(ProductStock m)
        {
            return Task.FromResult(false);
        }


        public virtual Task<Result> UpdateStockAsync(ProductStock m, int sn, bool isIncrease, bool isAudit, ParamOption option)
        {
            return Task.FromResult(Result.Success());
        }

        public virtual Task<ProductStock?> GetModelAsync(ProductStock m)
        {
            return Task.FromResult<ProductStock?>(null);
        }



        //public virtual ProductStock GetModel8Id(string id)
        //{
        //    return null;
        //}

        //public virtual ProductStock GetModel8Department(string department_id, string warehouse_id, string product_id)
        //{
        //    return null;
        //}

        //public virtual ProductStock GetModel8SerialNo(string department_id, string product_id, string serial_no)
        //{
        //    return null;
        //}

        //public virtual ProductStock GetModel8Shop(string shop_id, string warehouse_id, string product_id)
        //{
        //    return null;
        //}

        //public virtual string GetPage(ProductStock m)
        //{
        //    return null;
        //}


        //public virtual List<ProductStock> GetList(string department_id, string warehouse_id, string product_id)
        //{
        //    return null;
        //}

        //public virtual List<ProductStock> GetListForBatchNoChoice(string department_id, string warehouse_id, string product_id, string batch_no)
        //{
        //    return null;
        //}

        //public virtual List<ProductStock> GetListForSerialNoChoice(string department_id, string warehouse_id, string product_id, string batch_no, string color_id)
        //{
        //    return null;
        //}

        //public virtual List<ProductStock> GetListForSizeChoice(string department_id, string warehouse_id, string product_id, string group_id, string color_id)
        //{
        //    return null;
        //}

        //public List<ProductStock> GetListForTopSync(string department_id, string warehouse_id, string[] product_ids)
        //{
        //    var sb = new StatementBuilder();
        //    sb.Add("select d.ware_id,d.sku_id,sum(k.KCSL) quantity from KC_KCDTB k,WD_SPDM d where k." + base.ProductCodeFieldName + "=d.product_id");
        //    if (bookConfig.erp_type == ERPType.DRESS || bookConfig.erp_type == ERPType.CLOTHES)
        //    {
        //        sb.AddStatement("d.color_id=k.ysbm");
        //        sb.AddStatement("d.size_id=k.cmbm");
        //    }
        //    sb.AddStr("k.BMBM", department_id, 0);
        //    sb.AddStr("k.CKBM", warehouse_id);
        //    sb.AddInStatement("d.product_id", product_ids);
        //    sb.AddStr("d.department_id", department_id, 0);
        //    sb.Add("group by d.ware_id,d.sku_id");
        //    return dao.GetList8Sql<ProductStock>(sb.ToString());
        //}

        //public List<string> GetProductIdsForStoreSync(string department_id, string warehouse_id)
        //{
        //    var sb = new StatementBuilder();
        //    sb.Add($"select distinct {base.ProductCodeFieldName} from KC_KCDTB");
        //    sb.AddStr("BMBM", department_id, 0, "where");
        //    sb.AddStr("CKBM", warehouse_id);
        //    var dt = dao.GetDataTable(sb.ToString());
        //    var list = new List<string>();
        //    if (dt != null)
        //    {
        //        foreach (DataRow dr in dt.Rows)
        //        {
        //            list.Add(dr[0].ToString());
        //        }
        //    }
        //    return list;
        //}

        //public List<string> GetProductIdsForStoreSync(string department_id, string warehouse_id)
        //{
        //    var sb = new StatementBuilder();
        //    sb.Add($"select {base.ProductCodeFieldName},sum(KCSL) from KC_KCDTB");
        //    sb.AddStr("BMBM", department_id, 0, "where");
        //    sb.AddStr("CKBM", warehouse_id);
        //    sb.Add($"group by {base.ProductCodeFieldName} having sum(KCSL)>0");
        //    var dt = dao.GetDataTable(sb.ToString());
        //    var list = new List<string>();
        //    if (dt != null)
        //    {
        //        foreach (DataRow dr in dt.Rows)
        //        {
        //            list.Add(dr[0].ToString());
        //        }
        //    }
        //    return list;
        //}

    }
}
