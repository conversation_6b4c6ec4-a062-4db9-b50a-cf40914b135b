﻿using DarenApp.DAL.Sys;
using DarenCore.BLL.OldVersion;
using DarenCore.Interface;
using DarenCore.Models;

namespace DarenApp.BLL.Sys
{
    public class DigitsConfigBLL(IWebAppContext ctx) : BaseBLL<DigitsConfig, DigitsConfigDAL>(ctx)
    {
        public async Task<(List<DigitsConfig>, Exception?)> GetListAsync()
        {
            try
            {
                var list = await dal.GetListAsync();
                return (list, null);
            }
            catch (Exception ex)
            {
                return ([], ex);
            }
        }

        public async Task<(DigitsSetting?, Exception?)> GetConfig()
        {
            var (list, ex) = await GetListAsync();
            if (ex != null)
            {
                return (null, ex);
            }

            var setting = new DigitsSetting();

            if (list != null)
            {
                var item = list.Find(x => x.range.Contains(",SL,"));
                if (item != null)
                {
                    setting.sl = item.digits;
                }

                item = list.Find(x => x.range.Contains(",DJ,"));
                if (item != null)
                {
                    setting.jj = item.digits;
                    setting.sj = item.digits;
                }
                item = list.Find(x => x.range.Contains(",ZK,"));
                if (item != null)
                {
                    setting.zk = item.digits;
                }

                item = list.Find(x => x.range.Contains(",BCJF,"));
                if (item != null)
                {
                    setting.jf = item.digits;
                }
            }

            return (setting, null);
        }
    }
}