﻿using DarenCore.Common;
using DarenCore.DAL;

namespace DarenApp.DAL.Stock.ProductStockDAL
{
    public class ProductStockDALFactory : IDALFactory<IProductStockDAL>
    {
        public IProductStockDAL Create(ErpType type)
        {
            return type switch
            {
                ErpType.DRUG => new DrugProductStockDAL(),
                ErpType.INSTRUMENT => new InstrumentProductStockDAL(),
                _ => new CommonProductStockDAL(),
            };
        }
    }
}

