﻿using DarenApp.BLL.Sys;
using DarenApp.DAL.Dic.Enterprisex.Customerx;
using DarenApp.Models.Dic;
using DarenCore.BLL;
using DarenCore.BLL.OldVersion;
using DarenCore.Common;
using DarenCore.Extension;
using DarenCore.Interface;
using DarenCore.Utils;

namespace DarenApp.BLL.Dic
{
    public class CustomerBLL : BaseDicBLL<Customer, ICustomerDAL>, IControllerDicBLL<Customer>
    {


        public CustomerBLL()
        {

        }

        public CustomerBLL(IWebAppContext ctx) : base(ctx)
        {

        }

        public CustomerBLL(HttpContext ctx, string company_id) : base(ctx, company_id)
        {

        }

        public override async Task<(string key, string id, object? data, Exception?)> SaveModelAsync(Customer m, int sn)
        {

            if (m.is_new_row)
            {
                m.registration_date = DateUtils.CurrentDateTime();
            }

            if (string.IsNullOrEmpty(m.spell))
            {
                m.spell = GetFirstSpell(m.name);
            }

            try
            {
                await dal.SaveModelAsync(m, sn);
                return (m.key, m.id, m.spell, null);
            }
            catch (Exception ex)
            {
                return ("", "", null, ex);
            }
        }


        public async Task<Result> DeleteAsync(string id, int type)
        {
            if (id.IsNullOrEmpty())
            {
                return Result.Fail("参数错误！");
            }

            if (ErpPropJudge.CustomerIsMember(type) && !GetPowerOther("HY_Delete"))
            {
                return Result.Fail("您没有删除会员的权限！");
            }

            var r = await new TableService(ctx).CheckDicUseStatus1Aysnc("DWBM", id, "购货方");
            if (r.IsSuccess)
            {
                try
                {
                    await dal.DeleteByIdAsync(id, type);
                }
                catch (Exception ex)
                {
                    return Result.Fail(ex);
                }
            }
            return r;
        }

        public async Task<Result> DeletesAsync(string ids, int type)
        {
            if (ids.IsNullOrEmpty())
            {
                return Result.Fail("参数错误！");
            }

            if (ErpPropJudge.CustomerIsMember(type) && !GetPowerOther("HY_Delete"))
            {
                return Result.Fail("您没有删除会员的权限！");
            }

            var r = await new TableService(ctx).CheckDicUseStatus2Aysnc("DWBM", ids, "购货方");
            if (r.IsSuccess)
            {
                try
                {
                    await dal.DeleteByIdsAsync(ids, type);
                }
                catch (Exception ex)
                {
                    return Result.Fail(ex);
                }
            }
            return r;
        }

        public async Task<Result> GetMaxCodeAsync(string category_id)
        {
            var (code, ex) = await GetMaxCodeAsync(4, "LB", category_id);
            return Result.ByException(ex, code);
        }


        /// <summary>
        /// 更新往来余额
        /// </summary>
        /// <param name="id"></param>
        /// <param name="amount"></param>
        /// <param name="type"></param>
        /// <param name="isIncrease"></param>
        /// <returns></returns>
        public async Task<Result> UpdateCurrentBalanceAsync(string id, decimal amount, string type, bool isIncrease)
        {
            try
            {
                var (n, ex) = await dal.UpdateCurrentBalanceAsync(id, amount, type, isIncrease);
                if (ex != null)
                {
                    return Result.Fail(ex);
                }
                if (n != 1)
                {
                    return Result.Fail($"更新的行数为{n}");
                }
                return Result.Success();
            }
            catch (Exception ex)
            {
                return Result.Fail(ex);
            }
        }

        /// <summary>
        /// 更新会员积分信息
        /// </summary>
        /// <param name="id"></param>
        /// <param name="receivable"></param>
        /// <param name="sum_bonus"></param>
        /// <param name="use_bonus"></param>
        /// <param name="use_recharge"></param>
        /// <param name="isIncrease"></param>
        /// <returns></returns>
        public async Task<Result> UpdateMemberBonusAsync(string id, decimal receivable, decimal sum_bonus, decimal use_bonus, decimal use_recharge, bool isIncrease)
        {
            if (erpType == ErpType.INSTRUMENT)
            {
                return Result.Success();
            }


            var (m, ex) = await GetModelByCodeAsync(id);
            if (ex != null)
            {
                return Result.Fail(ex);
            }

            if (m == null)
            {
                return Result.Fail("会员不存在！");
            }

            if (!ErpPropJudge.CustomerIsMember(m.type))
            {
                return Result.Success();
            }

            //计算积分
            if (isIncrease)
            {
                m.sum_consumption += receivable;
                m.sum_bonus += sum_bonus;
                m.use_bonus += use_bonus;
                m.remaining_bonus = m.sum_bonus - m.use_bonus;
                m.latest_date = DateUtils.CurrentDateTime();
                if (use_bonus > 0 && m.remaining_bonus < 0)
                {
                    return Result.Fail("剩余积分不足");
                }
            }
            else
            {
                m.sum_consumption -= receivable;
                m.sum_bonus -= sum_bonus;
                m.use_bonus -= use_bonus;
                m.remaining_bonus = m.sum_bonus - m.use_bonus;
            }

            //获取自动升级信息
            if (m.sum_consumption > 0 && m.is_automatic_upgrade == 1)
            {
                var (l, e) = await new MemberLevelBLL(ctx).GetModel8ConsumptionAsync(m.sum_consumption);
                if (e != null)
                {
                    return Result.Fail(e);
                }

                if (l != null)
                {
                    m.member_level_id = l.id;
                    m.use_price = l.use_price;
                    m.use_discount = l.use_discount;
                }
            }

            //计算充值
            if (use_recharge > 0)
            {
                if (isIncrease)
                {
                    m.card_consumption += use_recharge;
                    m.card_balance -= use_recharge;
                    m.advance_balance -= use_recharge;
                    m.current_balance -= use_recharge;
                    if (m.card_balance < 0)
                    {
                        return Result.Fail("充值余额不足！");
                    }
                }
                else
                {
                    m.card_consumption -= use_recharge;
                    m.card_balance += use_recharge;
                    m.advance_balance += use_recharge;
                    m.current_balance += use_recharge;
                }
            }

            return await UpdateMemberBonusAsync(m);
         }

        /// <summary>
        /// 更新会员积分
        /// </summary>
        /// <param name="m"></param>
        /// <returns></returns>
        public async Task<Result> UpdateMemberBonusAsync(Customer m)
        {
            try
            {
                var n = await dal.SyncMemberBonusAsync(m);
                if (n != 1)
                {
                    return Result.Fail($"更新的行数为{n}");
                }
                return Result.Success();
            }
            catch (Exception ex)
            {
                return Result.Fail(ex);
            }
        }

        //更新会员级别信息
        public async Task<Result> UpdateMemberLevelInfo(MemberLevel m)
        {
            try
            {
                var n = await dal.UpdateMemberLevelAsync(m);
                if (n != 1)
                {
                    return Result.Fail($"更新的行数为{n}");
                }
                return Result.Success();
            }
            catch (Exception ex)
            {
                return Result.Fail(ex);
            }
        }


    }
}
