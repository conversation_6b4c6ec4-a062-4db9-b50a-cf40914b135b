﻿using DarenApp.Models.Dic;
using DarenCore.Common;
using DarenCore.DBUtils.SqlBuilder.Interfaces;
using System.Text;

namespace DarenApp.DAL.Dic.Enterprisex.Customerx
{
    public class DrugCustomerDAL : BaseCustomerDAL
    {

        public override Task<Result> SaveModelAsync(Customer m, int _)
        {
            IInsertUpdateBuilder<Customer> iu;

            if (m.is_new_row)
            {
                m.id = m.code;
                iu = CreateInsertBuilder();
                iu.AddItemDateTime("DJRQ", m.registration_date);
                iu.AddItemStr("BM", m.code);
            }
            else
            {
                iu = CreateUpdateBuilder();
                iu.AddWhereItemString("BM", m.id);

                iu.AddWhereItemDecimal("LJXF", m.sum_consumption_bak);
                iu.AddWhereItemDecimal("LJJF", m.sum_bonus_bak);
                iu.AddWhereItemDecimal("DXJF", m.use_bonus_bak);
                iu.AddWhereItemDecimal("SYJF", m.remaining_bonus_bak);

                iu.AddWhereItemDecimal("CARD_CZ", m.card_recharge_bak);
                iu.AddWhereItemDecimal("CARD_XF", m.card_consumption_bak);
                iu.AddWhereItemDecimal("CARD_YE", m.card_balance_bak);

                iu.AddWhereItemDecimal("YUSYE", m.advance_balance_bak);
                iu.AddWhereItemDecimal("YUFYE", m.prepaid_balance_bak);
                iu.AddWhereItemDecimal("YINSYE", m.receivable_balance_bak);
                iu.AddWhereItemDecimal("YINFYE", m.payable_balance_bak);
                iu.AddWhereItemDecimal("WLYE", m.current_balance_bak);
            }
            iu.AddItemStr("MC", m.name);
            iu.AddItemStr("DZ", m.address);
            iu.AddItemStr("DH", m.telephone);
            iu.AddItemStr("SJ", m.mobile);
            iu.AddItemStr("SFZ", m.person_number);
            iu.AddItemStr("NSBH", m.tax_code);
            iu.AddItemStr("DWBH", m.outer_code);
            iu.AddItemStr("KHYH", m.bank_name);
            iu.AddItemStr("YHZH", m.bank_number);
            iu.AddItemStr("JYFW", m.business_scope);
            iu.AddItemStr("WEBSITE", m.website);
            iu.AddItemStr("EMAIL", m.email);
            iu.AddItemStr("LXR", m.contacter);
            iu.AddItemStr("LLBM", m.department_id);
            iu.AddItemStr("LLR", m.linkman_id);
            iu.AddItemInt("XYTS", m.credit_days);
            iu.AddItemStr("JP", m.spell);
            iu.AddItemStr("LB", m.category_id);
            iu.AddItemStr("BZ", m.remark);
            iu.AddItemInt("DWLX", m.type);

            iu.AddItemDecimal("YUSYE", m.advance_balance);
            iu.AddItemDecimal("YUFYE", m.prepaid_balance);
            iu.AddItemDecimal("YINSYE", m.receivable_balance);
            iu.AddItemDecimal("YINFYE", m.payable_balance);
            iu.AddItemDecimal("WLYE", m.current_balance);

            iu.AddItemDecimal("LJXF", m.sum_consumption);
            iu.AddItemDecimal("LJJF", m.sum_bonus);
            iu.AddItemDecimal("DXJF", m.use_bonus);
            iu.AddItemDecimal("SYJF", m.remaining_bonus);
            iu.AddItemDecimal("CARD_CZ", m.card_recharge);
            iu.AddItemDecimal("CARD_XF", m.card_consumption);
            iu.AddItemDecimal("CARD_YE", m.card_balance);

            iu.AddItemStr("XB", m.sex);
            iu.AddItemStr("SR", m.birthday);
            iu.AddItemStr("DWZY", m.profession);
            iu.AddItemStr("JWBS", m.anamnesis);
            iu.AddItemStr("HYJB", m.member_level_id);
            iu.AddItemStr("SYJG", m.use_price);
            iu.AddItemDecimal("SYZK", m.use_discount);
            iu.AddItemStr("SB_FLAG", m.bonus_multiple == 2 ? "T" : "F");
            iu.AddItemBoolStr("SJ_FLAG", m.is_automatic_upgrade);
            iu.AddItemBoolStr("QY_FLAG", m.is_use);

            return SaveModelAsync(iu, SaveDataType.DIC_CODE);
        }

        public override string GetTableFields()
        {
            return FieldsBuilder.fields;
        }

        private class FieldsBuilder
        {
            private static StringBuilder Build()
            {
                var sb = new StringBuilder();
                sb.Append("BM id,BM code,MC name,DZ address,DH telephone,SJ mobile,SFZ person_number,");
                sb.Append("NSBH tax_code,DWBH outer_code,KHYH bank_name,YHZH bank_number,JYFW business_scope,WEBSITE website,EMAIL email,LXR contacter,");
                sb.Append("LLBM department_id,LLR linkman_id,XYTS credit_days,JP spell,LB category_id,BZ remark,DWLX type,DJRQ registration_date,");
                sb.Append("YUSYE advance_balance,YINSYE receivable_balance,YUFYE prepaid_balance,YINFYE payable_balance,WLYE current_balance,");

                sb.Append("LJXF sum_consumption,LJJF sum_bonus,DXJF use_bonus,SYJF remaining_bonus,");
                sb.Append("CARD_CZ card_recharge,CARD_XF card_consumption,CARD_YE card_balance,");
                sb.Append("XB sex,SR birthday,DWZY profession,JWBS anamnesis,OpenId openid,");
                sb.Append("HYJB member_level_id,SYJG use_price,SYZK use_discount,ZJXFRQ latest_date,");
                sb.Append("case when SB_FLAG='T' then 2 else 1 end bonus_multiple,");
                sb.Append("case when SJ_FLAG='T' then 1 else 0 end is_automatic_upgrade,");

                sb.Append("case when QY_FLAG='T' then 1 else 0 end is_use");
                return sb;
            }

            internal static readonly string fields = Build().ToString();
        }
    }
}
