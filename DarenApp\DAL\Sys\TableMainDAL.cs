﻿using DarenApp.Models.Sys;
using DarenCore.DAL.OldVersion;

namespace DarenApp.DAL.Sys
{
    public class TableMainDAL : BaseDAL<TableMain>
    {

        public override string? GetTableName()
        {
            return "GG_TB_ZB";
        }

        public Task<TableMain?> GetModelByNameAsync(string name)
        {
            return dao.GetModelByLimitAsync($"TableName='{name}'");
        }

        public override string GetTableFields()
        {
            return FieldsBuilder.fields;
        }

        private class FieldsBuilder
        {
            internal static readonly string fields = "ID id,TableName name,DisplayName display_name";
        }
    }
}