﻿using DarenApp.Models.Dic;
using DarenCore.Common;
using DarenCore.DBUtils.SqlBuilder.Interfaces;
using System.Text;

namespace DarenApp.DAL.Dic.Departmentx
{
    public class CommonDepartmentDAL : BaseDepartmentDAL
    {

        public override Task<Result> SaveModelAsync(Department m, int _)
        {
            IInsertUpdateBuilder<Department> iu;
            if (m.is_new_row)
            {
                m.id = m.code; 
                iu = CreateInsertBuilder();
                iu.AddItemStr("BM", m.code);
            }
            else
            {
                iu = CreateUpdateBuilder();
                iu.AddWhereItemString("BM", m.id);
            }
            iu.AddItemStr("MC", m.name);
            iu.AddItemStr("JP", m.spell);
            iu.AddItemStr("DZ", m.address);
            iu.AddItemStr("DH", m.telephone);
            iu.AddItemStr("LXR", m.contacter);
            iu.AddItemStr("LX", m.type);
            iu.AddItemStr("FL", m.pid);
            iu.AddItemInt("CC", m.layer);
            iu.AddItemBoolStr("QY_FLAG", m.is_use);

            return SaveModelAsync(iu, SaveDataType.DIC_CODE);
        }

        public override string GetTableFields()
        {
            return FieldsBuilder.fields;
        }

        private class FieldsBuilder
        {
            private static StringBuilder Build()
            {
                var sb = new StringBuilder();
                sb.Append("BM id,BM code,MC name,JP spell,");
                sb.Append("DZ address,DH telephone,LXR contacter,LX type,FL pid,CC layer,");
                sb.Append("case when QY_FLAG='T' then 1 else 0 end is_use");
                return sb;
            }

            internal static readonly string fields = Build().ToString();
        }

    }
}
