﻿using DarenApp.Models.Dic;
using DarenCore.Common;
using DarenCore.DBUtils.SqlBuilder.Interfaces;
using System.Text;

namespace DarenApp.DAL.Dic.Employeex
{
    public class CommonEmployeeDAL : BaseEmployeeDAL
    {
        public override Task<Result> SaveModelAsync(Employee m, int _)
        {
            IInsertUpdateBuilder<Employee> iu;
            if (m.is_new_row)
            {
                m.id = m.code;  
                iu = CreateInsertBuilder();
                iu.AddItemStr("BM", m.code);
                iu.AddItemStr("MM", m.login_password);
                iu.AddItemStr("PrintFormatDJ", "2");
                iu.AddItemStr("PrintFormatBB", "2");
            }
            else
            {
                iu = CreateUpdateBuilder();
                iu.AddWhereItemString("BM", m.id);
            }
            iu.AddItemStr("MC", m.name);
            iu.AddItemStr("XB", m.sex);
            iu.AddItemStr("SR", m.birthday);
            iu.AddItemStr("DZ", m.address);
            iu.AddItemStr("DH", m.telephone);
            iu.AddItemStr("SFZH", m.person_number);
            iu.AddItemStr("XL", m.education);
            iu.AddItemStr("ZC", m.professional);
            iu.AddItemStr("SGSJ", m.join_date);
            iu.AddItemStr("DLMC", m.login_name);
            iu.AddItemStr("BMBM", m.department_id);
            iu.AddItemBoolStr("QY_FLAG", m.is_use);

            return SaveModelAsync(iu, SaveDataType.DIC_CODE);
        }

        public override string GetTableFields()
        {
            return FieldsBuilder.fields;
        }

        private class FieldsBuilder
        {
            private static StringBuilder Build()
            {
                var sb = new StringBuilder();
                sb.Append("BM id,BM code,MC name,MM login_password,DLMC login_name,ID salt,");
                sb.Append("XB sex,SR birthday,DZ address,DH telephone,SFZH person_number,XL education,ZC professional,SGSJ join_date,");
                sb.Append("BMBM department_id,QX_BM power_department,QX_DJ power_bill,QX_CD power_menu,QX_QT power_other,");
                sb.Append("case when QY_FLAG='T' then 1 else 0 end is_use");
                return sb;
            }

            internal static readonly string fields = Build().ToString();
        }

    }
}
