﻿using DarenCore.Common;
using DarenCore.DBUtils;
using DarenCore.Extension;
using DarenCore.Interface;

namespace DarenApp.BLL.Sys
{
    public class TableService(IWebAppContext ctx)
    {
        private readonly IWebAppContext _ctx = ctx;
        private readonly EfcHelper dbhelper = CommonAssistant.CreateDbHelper(ctx.DbContext);

        /// <summary>
        /// 检查数据字典使用状态(单值)
        /// </summary>
        /// <param name="column_name"></param>
        /// <param name="column_value"></param>
        /// <param name="display_name"></param>
        /// <returns></returns>
        public async Task<Result> CheckDicUseStatus1Aysnc(string column_name, string column_value, string display_name)
        {
            try
            {
                var mainBLL = new TableMainBLL(_ctx);
                var (list, ex) = await new TableColumnBLL(_ctx).GetTableNameListByColumnNameAsync(column_name);
                if (ex != null)
                {
                    return Result.Fail(ex);
                }
                foreach (var table_name in list)
                {
                    var sql = $"select top 1 1 from {table_name} where {column_name}='{column_value}'";
                    var exist = dbhelper.GetInt(sql) > 0;
                    if (exist)
                    {
                        var (m, _) = await mainBLL.GetModelByNameAsync(table_name);
                        if (m != null)
                        {
                            return Result.Fail($"该{display_name}在[{m.display_name}]中已经使用，不能删除！");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return Result.Fail(ex.Message);
            }

            return Result.Success();
        }

        /// <summary>
        /// 检查数据字典使用状态(多值)
        /// </summary>
        /// <param name="column_name"></param>
        /// <param name="column_values"></param>
        /// <param name="display_name"></param>
        /// <returns></returns>
        public async Task<Result> CheckDicUseStatus2Aysnc(string column_name, string column_values, string display_name)
        {
            try
            {
                var mainBLL = new TableMainBLL(_ctx);
                var values = column_values.AddBrackets();
                var (list, ex) = await new TableColumnBLL(_ctx).GetTableNameListByColumnNameAsync(column_name);
                if (ex != null)
                {
                    return Result.Fail(ex);
                }
                foreach (var table_name in list)
                {
                    var sql = $"select top 1 1 from {table_name} where {column_name} in ({values})";
                    var exist = dbhelper.GetInt(sql) > 0;
                    if (exist)
                    {
                        var (m, _) = await mainBLL.GetModelByNameAsync(table_name);
                        if (m != null)
                        {
                            return Result.Fail($"该{display_name}在[{m.display_name}]中已经使用，不能删除！");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return Result.Fail(ex.Message);
            }

            return Result.Success();
        }

    }
}
