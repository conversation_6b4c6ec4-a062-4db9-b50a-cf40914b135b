﻿using DarenCore.Models.Interfaces;

namespace DarenCore.Models.DTO
{
    public class PageDTO<M> 
    {
        public M m { get; set; }

        public int pageIndex { get; set; }

        public int pageSize { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public string? orderby { get; set; }

        /// <summary>
        /// 模糊查询关键字
        /// </summary>
        public string? search { get; set; }
    }
}
