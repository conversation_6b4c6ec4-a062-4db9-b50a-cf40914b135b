{"Version": 1, "Hash": "OKJKSCJ+HW5z/acltFCEV7kfnqPV7Jo4+vTMhyiM128=", "Source": "<PERSON><PERSON><PERSON><PERSON>", "BasePath": "_content/DarenMain", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [{"Identity": "E:\\cursortest\\dear\\DarenApi\\DarenApi.csproj", "Version": 2, "Source": "DarenApi", "GetPublishAssetsTargets": "ComputeReferencedStaticWebAssetsPublishManifest;GetCurrentProjectPublishStaticWebAssetItems", "AdditionalPublishProperties": "Configuration=Debug;Platform=AnyCPU", "AdditionalPublishPropertiesToRemove": "WebPublishProfileFile;TargetFramework;RuntimeIdentifier;SelfContained", "GetBuildAssetsTargets": "GetCurrentProjectBuildStaticWebAssetItems", "AdditionalBuildProperties": "Configuration=Debug;Platform=AnyCPU", "AdditionalBuildPropertiesToRemove": "WebPublishProfileFile;TargetFramework;RuntimeIdentifier;SelfContained"}, {"Identity": "E:\\cursortest\\dear\\DarenApp\\DarenApp.csproj", "Version": 2, "Source": "DarenApp", "GetPublishAssetsTargets": "ComputeReferencedStaticWebAssetsPublishManifest;GetCurrentProjectPublishStaticWebAssetItems", "AdditionalPublishProperties": "Configuration=Debug;Platform=AnyCPU", "AdditionalPublishPropertiesToRemove": "WebPublishProfileFile;TargetFramework", "GetBuildAssetsTargets": "GetCurrentProjectBuildStaticWebAssetItems", "AdditionalBuildProperties": "Configuration=Debug;Platform=AnyCPU", "AdditionalBuildPropertiesToRemove": "WebPublishProfileFile;TargetFramework"}, {"Identity": "E:\\cursortest\\dear\\DarenCore\\DarenCore.csproj", "Version": 2, "Source": "DarenCore", "GetPublishAssetsTargets": "ComputeReferencedStaticWebAssetsPublishManifest;GetCurrentProjectPublishStaticWebAssetItems", "AdditionalPublishProperties": "Configuration=Debug;Platform=AnyCPU", "AdditionalPublishPropertiesToRemove": "WebPublishProfileFile;TargetFramework;RuntimeIdentifier;SelfContained", "GetBuildAssetsTargets": "GetCurrentProjectBuildStaticWebAssetItems", "AdditionalBuildProperties": "Configuration=Debug;Platform=AnyCPU", "AdditionalBuildPropertiesToRemove": "WebPublishProfileFile;TargetFramework;RuntimeIdentifier;SelfContained"}, {"Identity": "E:\\cursortest\\dear\\DarenWeb\\DarenWeb.csproj", "Version": 2, "Source": "DarenWeb", "GetPublishAssetsTargets": "ComputeReferencedStaticWebAssetsPublishManifest;GetCurrentProjectPublishStaticWebAssetItems", "AdditionalPublishProperties": "Configuration=Debug;Platform=AnyCPU", "AdditionalPublishPropertiesToRemove": "WebPublishProfileFile;TargetFramework", "GetBuildAssetsTargets": "GetCurrentProjectBuildStaticWebAssetItems", "AdditionalBuildProperties": "Configuration=Debug;Platform=AnyCPU", "AdditionalBuildPropertiesToRemove": "WebPublishProfileFile;TargetFramework"}], "DiscoveryPatterns": [], "Assets": [], "Endpoints": []}