﻿using DarenCore.Common;
using DarenCore.DAL;

namespace DarenApp.DAL.Bill.SalesReturn.Product
{
    public class SalesReturnProductDALFactory : IDALFactory<ISalesReturnProductDAL>
    {
        public ISalesReturnProductDAL Create(ErpType type)
        {
            return type switch
            {
                ErpType.DRUG => new DrugSalesReturnProductDAL(),
                _ => new InstrumentSalesReturnProductDAL(),
            };
        }
    }
}

