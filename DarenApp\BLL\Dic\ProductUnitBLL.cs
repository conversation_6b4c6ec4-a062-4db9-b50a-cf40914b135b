﻿using DarenApp.DAL.Dic.Common;
using DarenApp.Models.Dic;
using DarenCore.BLL;
using DarenCore.BLL.OldVersion;
using DarenCore.Common;
using DarenCore.Interface;
using DarenCore.Models.BaseDTO;
using DarenCore.Services;

namespace DarenApp.BLL.Dic
{

    public class ProductUnitBLL : BaseDicBLL<ProductUnit, ProductUnitDAL>, IControllerDicBLL<ProductUnit>
    {
        public ProductUnitBLL()
        {

        }

        public ProductUnitBLL(IWebAppContext ctx) : base(ctx)
        {

        }

        public override async Task<(string key, string id, object? data, Exception?)> SaveModelAsync(ProductUnit m, int sn)
        {
            if (string.IsNullOrEmpty(m.spell))
            {
                m.spell = GetFirstSpell(m.name);
            }

            try
            {
                await dal.SaveModelAsync(m, sn);
                return (m.key, m.id, m.spell, null);
            }
            catch (Exception ex)
            {
                return ("", "", null, ex);
            }
        }

        public Task<Result> SaveModelAsync(ProductUnit m)
        {
            return base.SaveModelAsync(m, true);
        }

        public Task<Result> SaveModelsAsync(List<ProductUnit> list)
        {
            return base.SaveModelsAsync(list, true);
        }
    }
}
