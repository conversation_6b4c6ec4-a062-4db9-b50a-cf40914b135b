﻿using DarenCore.DAL.OldVersion;
using DarenCore.Models;

namespace DarenApp.DAL.Sys.ParamConfigDAL
{
    public abstract class BaseParamConfigDAL : BaseDAL<ParamConfig>, IParamConfigDAL
    {
        public override string? GetTableName()
        {
            return "GG_XTBL";
        }

        public Task<ParamConfig?> GetModelAsync()
        {
            return dao.GetModelByLimitAsync();
        }

        public virtual Task<string?> GetNoticeAsync()
        {
            return Task.FromResult<string?>(null);
        }

        public Task<string?> GetAdminPasswordAsync()
        {
            var sb = CreateSelectBuilder();
            sb.AddField(x => x.admin_password);
            return dao.GetStringBySelectBuilderAsync(sb);
        }

        public Task<int> UpdateAdminPasswordAsync(string password)
        {
            var up = CreateUpdateBuilder();
            up.AddItemString(x => x.admin_password, password);
            return dao.UpdateAsync(up);
        }

    }
}