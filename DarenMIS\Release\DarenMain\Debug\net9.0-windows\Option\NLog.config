<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <variable name="logDirectory" value="${basedir}/Logs"/>
  <targets>
    <target xsi:type="File"
          name="file"
          layout="${longdate}|${level:uppercase=true}|${message}|${stacktrace}"
          archiveAboveSize="1024000"
          maxArchiveFiles="3"
          archiveFileName="${logDirectory}/Archives/log.{#}.log"
          archiveNumbering="Sequence"
          archiveEvery="Day"
          fileName="${logDirectory}/Logfile.log"
          createDirs="true"
          bufferSize="10240"
          autoFlush="true"
          keepFileOpen="true" />
  </targets>
  <rules>
    <logger name="*" minlevel="error" writeTo="file" />
  </rules>
</nlog>