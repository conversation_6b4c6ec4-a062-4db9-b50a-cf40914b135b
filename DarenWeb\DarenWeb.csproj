﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <OutputType>Exe</OutputType>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <BaseOutputPath>E:\cursortest\dear\DarenMIS\Release\DarenWEB</BaseOutputPath>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <GenerateAssemblyInfo>True</GenerateAssemblyInfo>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <GenerateAssemblyInfo>True</GenerateAssemblyInfo>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="7.2.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.Swagger" Version="7.2.0" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Newtonsoft.Json">
      <HintPath>..\..\..\vs2019\SysExtension\packages\Newtonsoft.Json.10.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="BLL\User\" />
    <Folder Include="DAL\User\" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\DarenCore\DarenCore.csproj" />
    <ProjectReference Include="..\DarenApp.Models\DarenApp.Models.csproj" />
    <ProjectReference Include="..\DarenShare\DarenShare.csproj" />
  </ItemGroup>

</Project>
