﻿using DarenApp.Models.Bill;
using DarenCore.Common;
using DarenCore.DAL.OldVersion;

namespace DarenApp.DAL.Bill
{

    public class BillSnDAL : BaseDAL<BillSn>
    {

        public override string? GetTableName()
        {
            return "GG_LSH";
        }

        public Task<Result> AddModelAsync(string employee_id, string bill_type, string bill_date, int sn)
        {
            var ib = CreateInsertBuilder();
            ib.AddItemStr("YGBM", employee_id);
            ib.AddItemStr("DJLX", ErpDataConvert.GetBillType(bill_type));
            ib.AddItemStr("RQ", bill_date);
            ib.AddItemInt("LSH", sn);
            return SaveModelAsync(ib, SaveDataType.DEFAULT);
        }

        public Task<BillSn?> GetModelAsync(string employee_id, string bill_type, string bill_date)
        {
            var wb = CreateWhereBuilder();
            wb.AddItemString("YGBM", employee_id);
            wb.AddItemString("DJLX", ErpDataConvert.GetBillType(bill_type));
            wb.AddItemString("RQ", bill_date);
            return dao.GetModelByWhereBuilderAsync(wb);
        }

        public Task<int> UpdateBillSn(string employee_id, string bill_type, string bill_date, int bill_sn)
        {
            var ub = CreateUpdateBuilder();
            ub.AddItemInt("LSH", bill_sn);
            ub.AddWhereItemString("YGBM", employee_id);
            ub.AddWhereItemString("DJLX", ErpDataConvert.GetBillType(bill_type));
            ub.AddWhereItemString("RQ", bill_date);
            return dao.UpdateAsync(ub);
        }

        //删除历史单据流水号
        public Task<int> DeleteHistoryModel(string employee_id, string bill_type)
        {
            var wb = CreateWhereBuilder();
            wb.AddItemString("YGBM", employee_id);
            wb.AddItemString("DJLX", ErpDataConvert.GetBillType(bill_type));
            return dao.DeleteByWhereBuilderAsync(wb);
        }

        public override string GetTableFields()
        {
            return "YGBM employee_id,DJLX bill_type,RQ bill_date,LSH bill_sn";
        }

    }
}