﻿using DarenCore.Common;
using DarenCore.DAL;
using DarenCore.Models;

namespace DarenApp.DAL.Sys.ParamConfigDAL
{
    public class ParamConfigDALFactory : IDALFactory<IParamConfigDAL>
    {
        public IParamConfigDAL Create(ErpType type)
        {
            if (type == ErpType.INSTRUMENT)
            {
                return new InstrumentParamConfigDAL();
            }
            return new CommonParamConfigDAL();
        }
    }
}

