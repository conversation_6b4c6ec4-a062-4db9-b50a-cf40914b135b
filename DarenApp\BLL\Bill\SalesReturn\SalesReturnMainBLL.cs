﻿using Climber.Model;
using DarenApp.DAL.Bill.SalesReturn.Main;
using DarenCore.Common;
using DarenCore.Interface;
using DarenCore.Utils;

namespace DarenApp.BLL.Bill.SalesReturn
{
    public class SalesReturnMainBLL(IWebAppContext ctx) : BillMainBaseBLL<SalesReturnMain, ISalesReturnMainDAL>(ctx)
    {
        public async Task<Result> SaveModelAsync(SalesReturnMain m)
        {
            var dt = DateUtils.CurrentDateTime();
            if (m.is_new_row)
            {
                m.add_date = dt;
                m.SetId(GetBillId(m.bill_no));
            }
            m.operator_id = user.user_id;
            m.operator_name = user.user_name;
            m.edit_date = dt;

            try
            {
                return await dal.SaveModelAsync(m);
            }
            catch (Exception ex)
            {
                return Result.Fail(ex);
            }
        }

        public Task<string> GetPageAsync(SalesReturnMain m, int pageIndex, int pageSize, string? orderby)
        {
            return dal.GetPageAsync(m, pageIndex, pageSize, orderby);
        }

    }
}

