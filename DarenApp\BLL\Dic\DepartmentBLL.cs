﻿using DarenApp.DAL.Dic.Departmentx;
using DarenApp.Models.Dic;
using DarenCore.BLL;
using DarenCore.BLL.OldVersion;
using DarenCore.Common;
using DarenCore.Interface;
using DarenCore.Services;

namespace DarenApp.BLL.Dic
{
    public class DepartmentBLL : BaseDicBLL<Department, IDepartmentDAL>, IControllerDicBLL<Department>
    {
        public DepartmentBLL()
        {

        }

        public DepartmentBLL(IWebAppContext ctx) : base(ctx)
        {

        }

        public override async Task<(string key, string id, object? data, Exception?)> SaveModelAsync(Department m, int sn)
        {
            if (string.IsNullOrEmpty(m.spell))
            {
                m.spell = GetFirstSpell(m.name);
            }

            try
            {
                await dal.SaveModelAsync(m, sn);
                return (m.key, m.id, m.spell, null);
            }
            catch (Exception ex)
            {
                return ("", "", null, ex);
            }
        }

        public Task<Result> SaveModelAsync(Department m)
        {
            return base.SaveModelAsync(m, true);
        }

        public Task<Result> SaveModelsAsync(List<Department> list)
        {
            return base.SaveModelsAsync(list, true);
        }

        public async Task<(Department?, Exception?)> GetModelByOuterCodeAsync(string outer_code)
        {
            try
            {
                var m = await dal.GetModelByOuterCodeAsync(outer_code);
                return (m, null);
            }
            catch (Exception ex)
            {
                return (null, ex);
            }
        }

        /// <summary>
        /// 获取列表数据
        /// </summary>
        /// <param name="m"></param>
        /// <param name="limitPower"></param>
        /// <returns></returns>
        public async Task<(List<Department>, Exception?)> GetListAsync(Department m, int limitPower = 0)
        {
            try
            {
                var list = await dal.GetListAsync(m, null, limitPower);
                return (list, null);
            }
            catch (Exception ex)
            {
                return ([], ex);
            }
        }

        /// <summary>
        /// 获取分页数据
        /// </summary>
        /// <param name="m"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="limitPower"></param>
        /// <returns></returns>
        public Task<string> GetPageJsonAsync(Department m, int pageIndex, int pageSize, int limitPower = 0)
        {
            return dal.GetPageJsonAsync(m, pageIndex, pageSize, null, limitPower);
        }


        //获取实际存货部门ID
        public static async Task<string> GetStockDepartmentId(IAbstractContext ctx, string id)
        {
            var (list, ex) = await DicService.Instance.GetDicCache<List<Department>>(ctx, nameof(Department));
            if (ex != null || list == null || list.Count == 0) return id;
            var m = list.Find(x => x.id == id);
            return m != null && m.type == "科室" ? m.pid : id;
        }

    }
}
