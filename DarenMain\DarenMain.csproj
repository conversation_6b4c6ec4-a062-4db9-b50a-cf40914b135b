<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <OutputType>Exe</OutputType>
    <BaseOutputPath>E:\cursortest\dear\DarenMIS\Release\DarenMain</BaseOutputPath>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <ProduceReferenceAssembly>False</ProduceReferenceAssembly>
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <GenerateAssemblyInfo>True</GenerateAssemblyInfo>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <GenerateAssemblyInfo>True</GenerateAssemblyInfo>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\DarenApi\DarenApi.csproj" />
    <ProjectReference Include="..\DarenApp\DarenApp.csproj" />
    <ProjectReference Include="..\DarenCore\DarenCore.csproj" />
    <ProjectReference Include="..\DarenApp.Models\DarenApp.Models.csproj" />
    <ProjectReference Include="..\DarenWeb\DarenWeb.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Properties\launchSettings.json">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>Never</CopyToPublishDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="StaticFiles\upload\" />
  </ItemGroup>

  <ProjectExtensions><VisualStudio><UserProperties option_4config_1json__JsonSchema="https://alec016.github.io/Custom-Machinery/Json%20Schema/src/main/resources/schemas/custom_machinery_recipe.json" /></VisualStudio></ProjectExtensions>

</Project>
