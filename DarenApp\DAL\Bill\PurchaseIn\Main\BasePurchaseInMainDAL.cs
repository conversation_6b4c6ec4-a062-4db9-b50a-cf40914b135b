using DarenApp.Models.Bill.PurchaseIn;
using DarenCore.Common;
using DarenCore.DAL.OldVersion;
using DarenCore.Extension;
using System.Text;

namespace DarenApp.DAL.Bill.PurchaseIn.Main
{
    public abstract class BasePurchaseInMainDAL : BaseBillMainDAL<PurchaseInMain>, IPurchaseInMainDAL
    {
        public override string? GetTableName()
        {
            return "RKD_ZB";
        }

        public Task<PurchaseInMain?> GetModelByOrderIdAsync(string id)
        {
            var wb = CreateWhereBuilder();
            wb.AddItemString("ID_DHD", id);
            return dao.GetModelByWhereBuilderAsync(wb);
        }

        public Task<string> GetPageAsync(PurchaseInMain m, int pageIndex, int pageSize, string? orderby)
        {

            var lb = CreateStatementBuilder();
            lb.AddErpDepartmentAll("BMBM", m.department_id, user);
            lb.AddDateRange("RQ", m.bill_date1, m.bill_date2);
            lb.AddStr("DWBM", m.enterprise_id);
            lb.AddDic("ZDR", m.maker_id);
            lb.AddDic("SHR", m.auditor_id);
            lb.AddBoolStr("SH", m.bill_status);

            if (!m.product_id.IsNullOrEmpty() || !m.category_id.IsNullOrEmpty())
            {
                lb.Add("and exists (");
                lb.Add("select 1 from RKD_MX M," + base.ProductTableName + " S");
                lb.Add("where M.ID = T.ID and M." + base.ProductCodeFieldName + " = S.BM");
                lb.AddStr("S.BM", m.product_id);
                lb.AddGroup("S.LB", m.category_id);
                lb.Add(")");
            }

            var sb = new StringBuilder();
            sb.Add("count(1) as total,");
            sb.Add("sum(ZSL) as quantity_sum,");
            sb.Add("sum(ZJE) as amount_sum,");
            sb.Add("sum(ZSE) as tax_sum,");
            sb.Add("sum(ZHJ) as total_sum,");
            sb.Add("sum(YJS) as payable_sum,");
            sb.Add("sum(EJS) as pay_sum,");
            sb.Add("sum(XJS) as payed_sum,");
            sb.Add("sum(WJS) as balance_sum");

            var p = new PaginationParam
            {
                limit = lb.ToString(),
                countFields = sb.ToString(),
                orderby = orderby,
                pageIndex = pageIndex,
                pageSize = pageSize
            };

            return dao.GetPageJsonAsync(p);
        }

    }

}
