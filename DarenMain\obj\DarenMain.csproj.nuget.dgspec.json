{"format": 1, "restore": {"E:\\cursortest\\dear\\DarenMain\\DarenMain.csproj": {}}, "projects": {"E:\\cursortest\\dear\\DarenApi\\DarenApi.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\cursortest\\dear\\DarenApi\\DarenApi.csproj", "projectName": "DarenApi", "projectPath": "E:\\cursortest\\dear\\DarenApi\\DarenApi.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\cursortest\\dear\\DarenApi\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\MicrosoftVisualStudio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "http://120.55.5.135:8022/nuget": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {"E:\\cursortest\\dear\\DarenApp.Models\\DarenApp.Models.csproj": {"projectPath": "E:\\cursortest\\dear\\DarenApp.Models\\DarenApp.Models.csproj"}, "E:\\cursortest\\dear\\DarenApp\\DarenApp.csproj": {"projectPath": "E:\\cursortest\\dear\\DarenApp\\DarenApp.csproj"}, "E:\\cursortest\\dear\\DarenCore\\DarenCore.csproj": {"projectPath": "E:\\cursortest\\dear\\DarenCore\\DarenCore.csproj"}, "E:\\cursortest\\dear\\DarenWeb.Models\\DarenWeb.Models.csproj": {"projectPath": "E:\\cursortest\\dear\\DarenWeb.Models\\DarenWeb.Models.csproj"}, "E:\\cursortest\\dear\\DarenWeb\\DarenWeb.csproj": {"projectPath": "E:\\cursortest\\dear\\DarenWeb\\DarenWeb.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "E:\\cursortest\\dear\\DarenApp.Models\\DarenApp.Models.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\cursortest\\dear\\DarenApp.Models\\DarenApp.Models.csproj", "projectName": "DarenApp.Models", "projectPath": "E:\\cursortest\\dear\\DarenApp.Models\\DarenApp.Models.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\cursortest\\dear\\DarenApp.Models\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\MicrosoftVisualStudio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "http://120.55.5.135:8022/nuget": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {"E:\\cursortest\\dear\\DarenCore\\DarenCore.csproj": {"projectPath": "E:\\cursortest\\dear\\DarenCore\\DarenCore.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"Microsoft.AspNetCore.Mvc.NewtonsoftJson": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "E:\\cursortest\\dear\\DarenApp\\DarenApp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\cursortest\\dear\\DarenApp\\DarenApp.csproj", "projectName": "DarenApp", "projectPath": "E:\\cursortest\\dear\\DarenApp\\DarenApp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\cursortest\\dear\\DarenApp\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\MicrosoftVisualStudio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "http://120.55.5.135:8022/nuget": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {"E:\\cursortest\\dear\\DarenApp.Models\\DarenApp.Models.csproj": {"projectPath": "E:\\cursortest\\dear\\DarenApp.Models\\DarenApp.Models.csproj"}, "E:\\cursortest\\dear\\DarenCore\\DarenCore.csproj": {"projectPath": "E:\\cursortest\\dear\\DarenCore\\DarenCore.csproj"}, "E:\\cursortest\\dear\\DarenShare\\DarenShare.csproj": {"projectPath": "E:\\cursortest\\dear\\DarenShare\\DarenShare.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"Microsoft.AspNetCore.Mvc.NewtonsoftJson": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[7.2.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "E:\\cursortest\\dear\\DarenCore\\DarenCore.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\cursortest\\dear\\DarenCore\\DarenCore.csproj", "projectName": "DarenCore", "projectPath": "E:\\cursortest\\dear\\DarenCore\\DarenCore.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\cursortest\\dear\\DarenCore\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\MicrosoftVisualStudio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "http://120.55.5.135:8022/nuget": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"Microsoft.AspNetCore.Mvc.NewtonsoftJson": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Caching.Memory": {"target": "Package", "version": "[9.0.0, )"}, "NLog": {"target": "Package", "version": "[5.3.4, )"}, "NPinyin.Core": {"target": "Package", "version": "[3.0.0, )"}, "SharpZipLib": {"target": "Package", "version": "[1.4.2, )"}, "System.Drawing.Common": {"target": "Package", "version": "[9.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "E:\\cursortest\\dear\\DarenMain\\DarenMain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\cursortest\\dear\\DarenMain\\DarenMain.csproj", "projectName": "<PERSON><PERSON><PERSON><PERSON>", "projectPath": "E:\\cursortest\\dear\\DarenMain\\DarenMain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\cursortest\\dear\\DarenMain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\MicrosoftVisualStudio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "http://120.55.5.135:8022/nuget": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {"E:\\cursortest\\dear\\DarenApi\\DarenApi.csproj": {"projectPath": "E:\\cursortest\\dear\\DarenApi\\DarenApi.csproj"}, "E:\\cursortest\\dear\\DarenApp.Models\\DarenApp.Models.csproj": {"projectPath": "E:\\cursortest\\dear\\DarenApp.Models\\DarenApp.Models.csproj"}, "E:\\cursortest\\dear\\DarenApp\\DarenApp.csproj": {"projectPath": "E:\\cursortest\\dear\\DarenApp\\DarenApp.csproj"}, "E:\\cursortest\\dear\\DarenCore\\DarenCore.csproj": {"projectPath": "E:\\cursortest\\dear\\DarenCore\\DarenCore.csproj"}, "E:\\cursortest\\dear\\DarenWeb\\DarenWeb.csproj": {"projectPath": "E:\\cursortest\\dear\\DarenWeb\\DarenWeb.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "E:\\cursortest\\dear\\DarenShare\\DarenShare.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\cursortest\\dear\\DarenShare\\DarenShare.csproj", "projectName": "DarenShare", "projectPath": "E:\\cursortest\\dear\\DarenShare\\DarenShare.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\cursortest\\dear\\DarenShare\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\MicrosoftVisualStudio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "http://120.55.5.135:8022/nuget": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {"E:\\cursortest\\dear\\DarenApp.Models\\DarenApp.Models.csproj": {"projectPath": "E:\\cursortest\\dear\\DarenApp.Models\\DarenApp.Models.csproj"}, "E:\\cursortest\\dear\\DarenCore\\DarenCore.csproj": {"projectPath": "E:\\cursortest\\dear\\DarenCore\\DarenCore.csproj"}, "E:\\cursortest\\dear\\DarenWeb.Models\\DarenWeb.Models.csproj": {"projectPath": "E:\\cursortest\\dear\\DarenWeb.Models\\DarenWeb.Models.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "E:\\cursortest\\dear\\DarenWeb.Models\\DarenWeb.Models.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\cursortest\\dear\\DarenWeb.Models\\DarenWeb.Models.csproj", "projectName": "DarenWeb.Models", "projectPath": "E:\\cursortest\\dear\\DarenWeb.Models\\DarenWeb.Models.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\cursortest\\dear\\DarenWeb.Models\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\MicrosoftVisualStudio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "http://120.55.5.135:8022/nuget": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {"E:\\cursortest\\dear\\DarenCore\\DarenCore.csproj": {"projectPath": "E:\\cursortest\\dear\\DarenCore\\DarenCore.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "E:\\cursortest\\dear\\DarenWeb\\DarenWeb.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\cursortest\\dear\\DarenWeb\\DarenWeb.csproj", "projectName": "DarenWeb", "projectPath": "E:\\cursortest\\dear\\DarenWeb\\DarenWeb.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\cursortest\\dear\\DarenWeb\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\MicrosoftVisualStudio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "http://120.55.5.135:8022/nuget": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {"E:\\cursortest\\dear\\DarenApp.Models\\DarenApp.Models.csproj": {"projectPath": "E:\\cursortest\\dear\\DarenApp.Models\\DarenApp.Models.csproj"}, "E:\\cursortest\\dear\\DarenCore\\DarenCore.csproj": {"projectPath": "E:\\cursortest\\dear\\DarenCore\\DarenCore.csproj"}, "E:\\cursortest\\dear\\DarenShare\\DarenShare.csproj": {"projectPath": "E:\\cursortest\\dear\\DarenShare\\DarenShare.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[7.2.0, )"}, "Swashbuckle.AspNetCore.Swagger": {"target": "Package", "version": "[7.2.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}