﻿using DarenApp.Models.Stock;
using DarenCore.Common;
using DarenCore.DBUtils.SqlBuilder.Interfaces;
using DarenCore.Extension;
using DarenCore.Models;
using System.Text;

namespace DarenApp.DAL.Stock.ProductStockDAL
{
    public class InstrumentProductStockDAL : BaseProductStockDAL
    {

        public override async Task<Result> SaveModelAsync(ProductStock m, int _)
        {
            IInsertUpdateBuilder<ProductStock> iu;
            var exist = await ExistsModelAsync(m);
            if (!exist)
            {
                iu = CreateInsertBuilder();
                iu.AddItemStr("BMBM", m.department_id);
                iu.AddItemStr("CKBM", m.warehouse_id);
                iu.AddItemStr("SPBM", m.product_id);
                iu.AddItemStr("SCPH", m.batch_no);
                iu.AddItemStr("MJPH", m.sterilize_no);
                iu.AddItemStr("PSID", m.serial_no);
            }
            else
            {
                iu = CreateUpdateBuilder();
                iu.AddWhereItemString("BMBM", m.department_id);
                iu.AddWhereItemString("CKBM", m.warehouse_id);
                iu.AddWhereItemString("SPBM", m.product_id);
                iu.AddWhereItemString("SCPH", m.batch_no);
                iu.AddWhereItemString("MJPH", m.sterilize_no);
                iu.AddWhereItemString("PSID", m.serial_no);
            }

            iu.AddItemDecimal("KCSL", m.quantity);
            iu.AddItemDecimal("CBJJ", m.cost_price);
            iu.AddItemDecimal("KCJE", m.amount);

            iu.AddItemInt("YXQX", m.expires_in);
            iu.AddItemDateTime("RKRQ", m.store_date);
            iu.AddItemDateTime("SCRQ", m.manufacture_date);
            iu.AddItemDateTime("YXQZ", m.expires_date);

            iu.AddItemStr("CKMC", m.warehouse_name);
            iu.AddItemStr("DWBM", m.supplier_id);
            iu.AddItemStr("DWMC", m.supplier_name);
            iu.AddItemStr("BZ", m.note);

            return await SaveModelAsync(iu, SaveDataType.DEFAULT);
        }

        public override Task<bool> ExistsModelAsync(ProductStock m)
        {
            var wb = CreateWhereBuilder();
            wb.AddItemString("BMBM", m.department_id);
            wb.AddItemString("CKBM", m.warehouse_id);
            wb.AddItemString("SPBM", m.product_id);
            wb.AddItemString("SCPH", m.batch_no);
            wb.AddItemString("MJPH", m.sterilize_no);
            wb.AddItemString("PSID", m.serial_no);
            return dao.ExistsByWhereBuilderAsync(wb);
        }

        public override Task<ProductStock?> GetModelAsync(ProductStock m)
        {
            var wb = CreateWhereBuilder();
            wb.AddItemString("BMBM", m.department_id);
            wb.AddItemString("CKBM", m.warehouse_id);
            wb.AddItemString("SPBM", m.product_id);
            wb.AddItemString("SCPH", m.batch_no);
            wb.AddItemString("MJPH", m.sterilize_no);
            wb.AddItemString("PSID", m.serial_no);
            return dao.GetModelByWhereBuilderAsync(wb);
        }

        public override async Task<Result> UpdateStockAsync(ProductStock m, int sn, bool isIncrease, bool isAudit, ParamOption option)
        {
            decimal quantity = 0, cost_price = 0, amount = 0;
            IInsertUpdateBuilder<ProductStock> iu;
            var e = await GetModelAsync(m);
            if (e == null)
            {
                iu = CreateInsertBuilder();
                iu.AddItemStr("BMBM", m.department_id);
                iu.AddItemStr("CKBM", m.warehouse_id);
                iu.AddItemStr("SPBM", m.product_id);
                iu.AddItemStr("SCPH", m.batch_no);
                iu.AddItemStr("MJPH", m.sterilize_no);
                iu.AddItemStr("PSID", m.serial_no);
                iu.AddItemStr("JGBM", "");
            }
            else
            {
                iu = CreateUpdateBuilder();
                iu.AddWhereItemString("BMBM", m.department_id);
                iu.AddWhereItemString("CKBM", m.warehouse_id);
                iu.AddWhereItemString("SPBM", m.product_id);
                iu.AddWhereItemString("SCPH", m.batch_no);
                iu.AddWhereItemString("MJPH", m.sterilize_no);
                iu.AddWhereItemString("PSID", m.serial_no);
                iu.AddWhereItemString("JGBM", "");
                quantity = e.quantity;
                amount = e.amount;
            }
            if (isIncrease)
            {
                quantity += m.quantity;
                amount += m.amount;
            }
            else
            {
                quantity -= m.quantity;
                amount -= m.amount;
            }
            if (quantity > 0)
            {
                cost_price = (amount / quantity).Fix(option.DigitsSetting.jj);
            }
            else if (quantity < 0 && option.ParamConfig.is_negative_stock == 0)
            {
                return Result.Fail((isAudit ? "审核成功" : "取消审核") + "后，第" + sn + "行商品的库存数量将为负数");
            }
            iu.AddItemDecimal("KCSL", quantity);
            iu.AddItemDecimal("CBJJ", cost_price);
            iu.AddItemDecimal("KCJE", amount);

            iu.AddItemInt("YXQX", m.expires_in);
            iu.AddItemDateTime("RKRQ", m.store_date);
            iu.AddItemDateTime("SCRQ", m.manufacture_date);
            iu.AddItemDateTime("YXQZ", m.expires_date);
            iu.AddItemDateTime("MJRQ", m.sterilize_date);
            iu.AddItemDateTime("MJXQ", m.sterilize_exp);

            iu.AddItemStr("CKMC", m.warehouse_name);
            iu.AddItemStr("DWBM", m.supplier_id);
            iu.AddItemStr("DWMC", m.supplier_name);
            iu.AddItemStr("BZ", m.note);

            return await SaveModelAsync(iu, SaveDataType.DEFAULT);
        }

        //public override List<ProductStock> GetList(string department_id, string warehouse_id, string product_id)
        //{
        //    var sb = new StatementBuilder();
        //    sb.AddStr("BMBM", department_id);
        //    sb.AddStr("CKBM", warehouse_id);
        //    sb.AddStr("SPBM", product_id);
        //    sb.Add("and KCSL>0");
        //    return dao.GetList8Limit<ProductStock>(sb.ToString(), "YXQZ,SCPH,PSID");
        //}

        //public override List<ProductStock> GetListForBatchNoChoice(string department_id, string warehouse_id, string product_id, string batch_no)
        //{
        //    var sb = new StatementBuilder();
        //    sb.AddStr("BMBM", department_id);
        //    sb.AddStr("CKBM", warehouse_id);
        //    sb.AddStr("SPBM", product_id);
        //    sb.AddStr("SCPH", batch_no);
        //    sb.Add("and KCSL>0");
        //    return dao.GetList8Limit<ProductStock>(sb.ToString(), "YXQZ,SCPH,PSID");
        //}

        //public override ProductStock GetModel8SerialNo(string department_id, string product_id, string serial_no)
        //{
        //    var sb = new StatementBuilder();
        //    sb.AddStr("BMBM", department_id);
        //    sb.AddStr("SPBM", product_id);
        //    sb.AddStr("PSID", serial_no);
        //    return dao.GetModel8Limit<ProductStock>(sb.ToString());
        //}

        public override string GetTableFields()
        {
            return FieldsBuilder.fields;
        }

        private class FieldsBuilder
        {
            private static StringBuilder Build()
            {
                var sb = new StringBuilder();
                sb.Append("BMBM department_id,CKBM warehouse_id,CKMC warehouse_name,");
                sb.Append("SPBM product_id,SCPH batch_no,SCRQ manufacture_date,YXQX expires_in,YXQZ expires_date,");
                sb.Append("MJPH sterilize_no,MJRQ sterilize_date,MJXQ sterilize_exp,PSID serial_no,");
                sb.Append("KCSL quantity,CBJJ cost_price,KCJE amount,BZ note");
                return sb;
            }

            internal static readonly string fields = Build().ToString();
        }
    }
}
