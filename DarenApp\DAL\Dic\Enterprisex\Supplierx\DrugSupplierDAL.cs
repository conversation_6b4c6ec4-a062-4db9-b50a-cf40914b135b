﻿using DarenApp.Models.Dic;
using DarenCore.Common;
using DarenCore.DBUtils.SqlBuilder.Interfaces;
using System.Text;

namespace DarenApp.DAL.Dic.Enterprisex.Supplierx
{
    public class DrugSupplierDAL : BaseSupplierDAL
    {

        public override Task<Result> SaveModelAsync(Supplier m, int _)
        {
            IInsertUpdateBuilder<Supplier> iu;

            if (m.is_new_row)
            {
                m.id = m.code;
                iu = CreateInsertBuilder();
                iu.AddItemDateTime("DJRQ", m.registration_date);
                iu.AddItemStr("BM", m.code);
            }
            else
            {
                iu = CreateUpdateBuilder();
                iu.AddWhereItemString("BM", m.id);
                iu.AddWhereItemDecimal("YUSYE", m.advance_balance_bak);
                iu.AddWhereItemDecimal("YUFYE", m.prepaid_balance_bak);
                iu.AddWhereItemDecimal("YINSYE", m.receivable_balance_bak);
                iu.AddWhereItemDecimal("YINFYE", m.payable_balance_bak);
                iu.AddWhereItemDecimal("WLYE", m.current_balance_bak);
            }

            iu.AddItemStr("MC", m.name);
            iu.AddItemStr("DZ", m.address);
            iu.AddItemStr("DH", m.telephone);
            iu.AddItemStr("SJ", m.mobile);
            iu.AddItemStr("NSBH", m.tax_code);
            iu.AddItemStr("DWBH", m.outer_code);
            iu.AddItemStr("KHYH", m.bank_name);
            iu.AddItemStr("YHZH", m.bank_number);
            iu.AddItemStr("JYFW", m.business_scope);
            iu.AddItemStr("WEBSITE", m.website);
            iu.AddItemStr("EMAIL", m.email);
            iu.AddItemStr("LXR", m.contacter);
            iu.AddItemStr("LLBM", m.department_id);
            iu.AddItemStr("LLR", m.linkman_id);
            iu.AddItemStr("JP", m.spell);
            iu.AddItemStr("LB", m.category_id);
            iu.AddItemStr("BZ", m.remark);
            iu.AddItemInt("DWLX", 1);

            iu.AddItemDecimal("YUSYE", m.advance_balance);
            iu.AddItemDecimal("YUFYE", m.prepaid_balance);
            iu.AddItemDecimal("YINSYE", m.receivable_balance);
            iu.AddItemDecimal("YINFYE", m.payable_balance);
            iu.AddItemDecimal("WLYE", m.current_balance);
            iu.AddItemDecimal("XYTS", m.credit_days);

            iu.AddItemBoolStr("QY_FLAG", m.is_use);

            return SaveModelAsync(iu, SaveDataType.DIC_CODE);
        }

        public override string GetTableFields()
        {
            return FieldsBuilder.fields;
        }

        private class FieldsBuilder
        {
            private static StringBuilder Build()
            {
                var sb = new StringBuilder();
                sb.Append("BM id,BM code,MC name,DZ address,DH telephone,SJ mobile,");
                sb.Append("NSBH tax_code,DWBH outer_code,KHYH bank_name,YHZH bank_number,JYFW business_scope,WEBSITE website,EMAIL email,LXR contacter,");
                sb.Append("LLBM department_id,LLR linkman_id,JP spell,LB category_id,BZ remark,DWLX type,DJRQ registration_date,");
                sb.Append("YUSYE advance_balance,YINSYE receivable_balance,YUFYE prepaid_balance,YINFYE payable_balance,WLYE current_balance,XYTS credit_days,");
                sb.Append("case when QY_FLAG='T' then 1 else 0 end is_use");
                return sb;
            }

            internal static readonly string fields = Build().ToString();
        }
    }
}
