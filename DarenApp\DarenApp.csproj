﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
     <TargetFramework>net9.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <OutputType>Exe</OutputType>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);1591</NoWarn>
    <BaseOutputPath>E:\cursortest\dear\DarenMIS\Release\DarenApp</BaseOutputPath>
    <DocumentationFile></DocumentationFile>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <NoWarn>1701;1702;8618</NoWarn>
    <WarningsAsErrors>$(WarningsAsErrors);NU1605</WarningsAsErrors>
    <WarningLevel>9999</WarningLevel>
    <GenerateAssemblyInfo>True</GenerateAssemblyInfo>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <NoWarn>1701;1702;8618</NoWarn>
    <WarningsAsErrors>$(WarningsAsErrors);NU1605</WarningsAsErrors>
    <WarningLevel>9999</WarningLevel>
    <GenerateAssemblyInfo>True</GenerateAssemblyInfo>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Library\**" />
    <Compile Remove="Packages\**" />
    <Content Remove="Library\**" />
    <Content Remove="Packages\**" />
    <EmbeddedResource Remove="Library\**" />
    <EmbeddedResource Remove="Packages\**" />
    <None Remove="Library\**" />
    <None Remove="Packages\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="9.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="7.2.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\DarenCore\DarenCore.csproj" />
    <ProjectReference Include="..\DarenApp.Models\DarenApp.Models.csproj" />
    <ProjectReference Include="..\DarenShare\DarenShare.csproj" />
  </ItemGroup>

</Project>
