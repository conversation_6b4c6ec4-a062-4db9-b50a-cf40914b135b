using DarenApp.Models.Dic;
using DarenCore.Common;
using DarenCore.DAL.OldVersion;

namespace DarenApp.DAL.Dic.Departmentx
{
    public interface IDepartmentDAL : IBaseDicDAL<Department>
    {
        Task<Department?> GetModelByOuterCodeAsync(string outer_code);

        Task<List<Department>> GetListAsync(Department m, string? orderby, int limitPower);

        Task<string> GetPageJsonAsync(Department m, int pageIndex, int pageSize, string? orderby, int limitPower);
    }
}
