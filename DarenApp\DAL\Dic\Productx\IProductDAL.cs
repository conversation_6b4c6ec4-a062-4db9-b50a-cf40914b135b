using DarenApp.Models.Dic;
using DarenCore.DAL.OldVersion;
using Newtonsoft.Json.Linq;

namespace DarenApp.DAL.Dic.Productx
{
    public interface IProductDAL : IBaseDicDAL<Product>
    {
        //Result SaveModel(Product m);

        //Result SaveModels(List<Product> m);

        //List<Product> GetList(Product m);

        //string GetPage(Product m);

        //string GetStockPage(Product m, string department_id, string warehouse_id);

        //Product GetModel8Barcode(string barcode);

        //decimal GetPrice(string product_id, string price_type);

        //Result UpdateCommissionInfo(ProductMarketing m);


        Task<JObject?> GetPageJArrayForWwzAsync(int pageIndex, int pageSize);

    }

}
