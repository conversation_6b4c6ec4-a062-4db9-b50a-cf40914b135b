﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace DarenCore.Models.BaseModel
{
    public class BaseBillSettlementModel : BaseChildenModel
    {

        [DisplayName("结算方式")]
        [StringLength(255)]
        [Required]
        public string settlement_id { get; set; }

        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string settlement_name { get; set; }

        [DisplayName("结算金额")]
        public decimal settlement_amount { get; set; }

        [DisplayName("结算类型")]
        public int settlement_type { get; set; }

        [DisplayName("会计科目")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string subject_id { get; set; }

        [DisplayName("科目全称")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string subject_name { get; set; }

        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string subject1_id { get; set; }

        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string subject2_id { get; set; }

        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string subject3_id { get; set; }

        [DisplayName("票据号码")]
        [StringLength(255)]
        [DisplayFormat(ConvertEmptyStringToNull = false)]
        public string doc_no { get; set; }

    }
}