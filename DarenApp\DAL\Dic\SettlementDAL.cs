﻿using DarenApp.Models.Dic;
using DarenCore.Common;
using DarenCore.DAL.OldVersion;
using DarenCore.DBUtils.SqlBuilder.Interfaces;
using DarenCore.Extension;
using System.Text;

namespace DarenApp.DAL.Dic
{
    public class SettlementDAL : BaseDicDAL<Settlement>
    {
        public override string? GetTableName()
        {
            return "BM_JSFS";
        }

        public override Task<Result> SaveModelAsync(Settlement m, int _)
        {
            IInsertUpdateBuilder<Settlement> iu;
            if (m.is_new_row)
            {
                m.id = m.code;
                iu = CreateInsertBuilder();
                iu.AddItemStr("BM", m.code);
            }
            else
            {
                iu = CreateUpdateBuilder();
                iu.AddWhereItemString("BM", m.id);
            }
            iu.AddItemStr("MC", m.name);
            iu.AddItemInt("LX", m.type);
            iu.AddItemStr("KMBM", m.subject_id);
            iu.AddItemStr("KMQC", m.subject_name);
            iu.AddItemStr("KM1BM", m.subject1_id);
            iu.AddItemStr("KM2BM", m.subject2_id);
            iu.AddItemStr("KM3BM", m.subject3_id);
            iu.AddItemBoolInt("QY_FLAG", m.is_use);
            return SaveModelAsync(iu, SaveDataType.DIC_CODE);
        }

        public override string GetLimit(Settlement m)
        {
            var sb = CreateStatementBuilder();
            sb.AddLikeBoth("code", m.code);
            sb.AddLikeBoth("name", m.name);
            if (!m.search.IsNullOrEmpty())
            {
                sb.AddLikeBoth("code", m.search, null, "and (");
                sb.AddLikeBoth("name", m.search, "or", null, ")");
            }
            sb.AddBoolStr("QY_FLAG", m.is_use);
            return sb.ToString();
        }

        public override string GetTableFields()
        {
            return FieldsBuilder.fields;
        }

        private class FieldsBuilder
        {
            private static StringBuilder Build()
            {
                var sb = new StringBuilder();
                sb.Append("BM id,BM code,MC name,LX type,");
                sb.Append("KMBM subject_id,KMQC subject_name,KM1BM subject1_id,KM2BM subject2_id,KM3BM subject3_id,");
                sb.Append("case when QY_FLAG='T' then 1 else 0 end is_use");
                return sb;
            }

            internal static readonly string fields = Build().ToString();
        }
    }
}
