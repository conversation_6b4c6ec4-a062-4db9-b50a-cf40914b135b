﻿using DarenApp.DAL.Dic.Employeex;
using DarenApp.Models.Dic;
using DarenCore.BLL;
using DarenCore.BLL.OldVersion;
using DarenCore.Common;
using DarenCore.Extension;
using DarenCore.Interface;
using DarenCore.Models.DTO;
using DarenWeb.Models.Sys;

namespace DarenApp.BLL.Dic
{
    public class EmployeeBLL: BaseDicBLL<Employee, IEmployeeDAL>, IControllerDicBLL<Employee>
    {
        private readonly IWebAppContext _ctx;

        public EmployeeBLL()
        {

        }

        public EmployeeBLL(IWebAppContext ctx) : base(ctx)
        {
            _ctx = ctx;
        }

        public override async Task<(string key, string id, object? data, Exception?)> SaveModelAsync(Employee m, int sn)
        {
            if (m.is_new_row)
            {
                m.login_password = _ctx.AppOptions.Setting.NewUserPassword;
            }

            if (m.login_name.IsNullOrEmpty())
            {
                m.login_name = GetFirstSpell(m.name);
            }

            try
            {
                await dal.SaveModelAsync(m, sn);
                return (m.key, m.id, m.login_name, null);
            }
            catch (Exception ex)
            {
                return ("", "", null, ex);
            }
        }

        public Task<Result> SaveModelAsync(Employee m)
        {
            return base.SaveModelAsync(m, true);
        }

        public Task<Result> SaveModelsAsync(List<Employee> list)
        {
            return base.SaveModelsAsync(list, true);
        }

        public async Task<(Employee?, Exception?)> GetModelForLogin(string code)
        {
            try
            {
                var m = await dal.GetModelForLoginAsync(code);
                return (m, null);
            }
            catch (Exception ex)
            {
                return (null, ex);
            }
        }

        public async Task<Result> GetMaxCodeAsync(string department_id)
        {
            var (code, ex) = await GetMaxCodeAsync(2, "BMBM", department_id);
            return Result.ByException(ex, code);
        }
    }
}
