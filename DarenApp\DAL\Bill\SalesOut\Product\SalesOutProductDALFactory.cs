﻿using DarenCore.Common;
using DarenCore.DAL;

namespace DarenApp.DAL.Bill.SalesOut.Product
{
    public class SalesOutProductDALFactory : IDALFactory<ISalesOutProductDAL>
    {
        public ISalesOutProductDAL Create(ErpType type)
        {
            return type switch
            {
                ErpType.DRUG => new DrugSalesOutProductDAL(),
                _ => new InstrumentSalesOutProductDAL(),
            };
        }
    }
}

