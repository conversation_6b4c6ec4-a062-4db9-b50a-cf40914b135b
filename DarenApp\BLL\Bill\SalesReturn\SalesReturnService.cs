﻿using Climber.Model;
using DarenApp.BLL.Dic;
using DarenApp.Models.Bill.SalesReturn;
using DarenCore.Common;
using DarenCore.Extension;
using DarenCore.Interface;
using DarenCore.Models;
using DarenCore.Utils;

namespace DarenApp.BLL.Bill.SalesReturn
{
    public class SalesReturnService(IWebAppContext ctx)
    {
        private readonly IWebAppContext _ctx = ctx;
        private readonly OnlineUser _user = ctx.User;
        private readonly ParamConfig _config = ctx.ParamOption.ParamConfig;

        public Task<Result> SaveAsync(SalesReturnBill bill)
        {
            var b = new SalesReturnBuilder(_ctx, bill);
            var r = BillDirector.SaveBillAsync(b);
            return r;
        }

        public Task<Result> AuditAsync(SalesReturnBill bill)
        {
            var b = new SalesReturnBuilder(_ctx, bill);
            var r = BillDirector.AuditBillAsync(b);
            return r;
        }

        public async Task<Result> CancelAsync(SalesReturnBill? bill)
        {
            if (bill == null)
            {
                return Result.Fail("单据为空!");
            }

            if (bill.main.bill_status == 0)
            {
                return Result.Fail("单据状态为[待审核]，不能反审核！");
            }

            if (bill.main.paid != bill.main.payable)
            {
                return Result.Fail("单据已关联其它结算类单据，不能反审核！");
            }

            if (!bill.main.voucher_no.IsNullOrEmpty())
            {
                return Result.Fail("单据状态为[已记账]，反审前需先反记账(编制)！");
            }

            var b = new SalesReturnBuilder(_ctx, bill);
            var r = await BillDirector.CancelBillAsync(b);
            return r;
        }

        public async Task<Result> CancelAsync(string id)
        {
            if (id.IsNullOrEmpty())
            {
                return Result.Fail("参数错误！");
            }

            var (m, ex) = await GetBillByIdAsync(id);
            if (ex != null)
            {
                return Result.Fail(ex);
            }

            var r = await CancelAsync(m);
            return r;
        }

        public async Task<Result> DeleteAsync(string id)
        {
            if (id.IsNullOrEmpty())
            {
                return Result.Fail("参数错误！");
            }

            var (main, ex) = await new SalesReturnMainBLL(_ctx).GetModelByIdAsync(id);
            if (ex != null)
            {
                return Result.Fail(ex);
            }

            if (main == null)
            {
                return Result.Fail("单据不存在！");
            }

            if (main.bill_status == 1)
            {
                return Result.Fail("单据状态为[已审核]，不能删除！");
            }

            try
            {
                await _ctx.DbContext.BeginTransAsync();

                var r = await new SalesReturnMainBLL(_ctx).DeleteByIdAsync(id);
                if (r.IsFail)
                {
                    await _ctx.DbContext.RollbackAsync();
                    return r;
                }

                r = await new SalesReturnProductBLL(_ctx).DeleteByPidAsync(id);
                if (r.IsFail)
                {
                    await _ctx.DbContext.RollbackAsync();
                    return r;
                }

                r = await new SalesReturnSettlementBLL(_ctx).DeleteByPidAsync(id);
                if (r.IsFail)
                {
                    await _ctx.DbContext.RollbackAsync();
                    return r;
                }

                await _ctx.DbContext.CommitAsync();

                return Result.Success();
            }
            catch (Exception err)
            {
                return Result.Fail(err);
            }
        }

        /// <summary>
        /// 根据ID获取单据 - 在视图中使用
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<(SalesReturnBill?, Exception?)> GetBillForViewAsync(string id)
        {
            SalesReturnBill? m = null;

            if (!id.IsNullOrEmpty())
            {
                var (main, ex) = await new SalesReturnMainBLL(_ctx).GetModelByIdAsync(id);
                if (ex != null)
                {
                    return (null, ex);
                }

                if (main != null)
                {
                    (m, ex) = await MergeBillAsync(main);
                    if (ex != null)
                    {
                        return (null, ex);
                    }
                }
            }

            if (m == null)
            {
                m = new SalesReturnBill();
                m.main.bill_date = DateUtils.CurrentDateTime();
                m.main.enterprise_id = _config.default_customer_id;
                m.main.maker_id = _user.user_id;
                m.main.maker_name = _user.user_name;
                if (!_user.is_admin)
                {
                    m.main.department_id = _user.department_id;
                    m.main.handler_id = _user.user_id;
                }
            }

            if (!m.main.enterprise_id.IsNullOrEmpty())
            {
                (m.customer, var ex) = await new CustomerBLL(_ctx).GetModelByIdAsync(m.main.enterprise_id);
                if (ex != null)
                {
                    return (null, ex);
                }
            }

            return (m, null);
        }

        /// <summary>
        /// 根据ID获取单据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<(SalesReturnBill?, Exception?)> GetBillByIdAsync(string id)
        {
            var (main, ex) = await new SalesReturnMainBLL(_ctx).GetModelByIdAsync(id);
            if (ex != null)
            {
                return (null, ex);
            }

            if (main == null)
            {
                return (null, new Exception("单号不存在"));
            }

            return await MergeBillAsync(main);
        }

        /// <summary>
        /// 合并单据
        /// </summary>
        /// <param name="main"></param>
        /// <returns></returns>
        private async Task<(SalesReturnBill?, Exception?)> MergeBillAsync(SalesReturnMain main)
        {
            var (products, ex1) = await new SalesReturnProductBLL(_ctx).GetListByPidAsync(main.id);
            if (ex1 != null)
            {
                return (null, ex1);
            }

            var (settlements, ex2) = await new SalesReturnSettlementBLL(_ctx).GetListByPidAsync(main.id);
            if (ex2 != null)
            {
                return (null, ex2);
            }

            var bill = new SalesReturnBill(main, products, settlements);
            return (bill, null);
        }
    }
}
