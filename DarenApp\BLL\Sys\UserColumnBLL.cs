﻿using DarenApp.DAL.Sys;
using DarenApp.Models.Bill.SalesReturn;
using DarenApp.Models.Sys;
using DarenCore.BLL.OldVersion;
using DarenCore.Common;
using DarenCore.Interface;
using DarenCore.Utils;

namespace DarenApp.BLL.Sys
{
    public class UserColumnBLL(IWebAppContext ctx) : BaseBLL<UserColumn, UserColumnDAL>(ctx)
    {

        public async Task<Result> SaveModelsAsync(List<UserColumn> list, string component)
        {
            var i = 0;
            var dt = DateUtils.CurrentDateTime();
            foreach (var m in list)
            {
                i++;
                if (m.is_new_row)
                {
                    m.SetId(GetComId(i));
                    m.add_date = dt;
                }
                m.sn = i;
                m.component = component;
                m.employee_id = user.user_id;
            }

            try
            {
                await DeleteColumnsAsync(component);
                await DbHelper.BeginTransAsync();
                var r = await dal.SaveModelsAsync(list);
                if (r.IsSuccess)
                {
                    await DbHelper.CommitAsync();
                }
                else
                {
                    await DbHelper.RollbackAsync();
                }
                return r;
            }
            catch (Exception ex)
            {
                return Result.Fail(ex);
            }
        }

        public async Task<Result> DeleteColumnsAsync(string component)
        {
            try
            {
                await dal.DeleteColumnsAsync(component);
                return Result.Success();
            }
            catch (Exception ex)
            {
                return Result.Fail(ex);
            }
        }



    }
}
