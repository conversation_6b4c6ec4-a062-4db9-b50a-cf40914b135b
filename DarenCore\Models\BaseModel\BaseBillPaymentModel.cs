﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace DarenCore.Models.BaseModel
{
    public class BaseBillPaymentModel : BaseIdModel
    {
        [StringLength(255)]
        [Required]
        public string pid { get; set; }

        [DisplayName("支付方式")]
        [StringLength(255)]
        [Required]
        public string payment_id { get; set; }

        [DisplayName("支付金额")]
        public decimal payment_amount { get; set; }
    }
}