﻿using DarenApp.Models.Dic;
using DarenCore.Common;
using DarenCore.DBUtils.SqlBuilder.Interfaces;
using System.Text;

namespace DarenApp.DAL.Dic.Warehousex
{
    public class DrugWarehouseDAL : BaseWarehouseDAL
    {
        public override Task<Result> SaveModelAsync(Warehouse m, int _)
        {
            IInsertUpdateBuilder<Warehouse> iu;
            if (m.is_new_row)
            {
                m.id = m.code;
                iu = CreateInsertBuilder();
                iu.AddItemStr("BM", m.code);
            }
            else
            {
                iu = CreateUpdateBuilder();
                iu.AddWhereItemString("BM", m.id);
            }
            iu.AddItemStr("MC", m.name);
            iu.AddItemStr("JP", m.spell);
            iu.AddItemStr("DZ", m.address);
            iu.AddItemStr("DH", m.telephone);
            iu.AddItemStr("LXR", m.contact);
            iu.AddItemStr("CCTJ", m.storage_condition);
            iu.AddItemStr("BMBM", m.department_id);
            iu.AddItemBoolStr("QY_FLAG", m.is_use);
            return SaveModelAsync(iu, SaveDataType.DIC_CODE);
        }

        public override string GetTableFields()
        {
            return FieldsBuilder.fields;
        }

        private class FieldsBuilder
        {
            private static StringBuilder Build()
            {
                var sb = new StringBuilder();
                sb.Append("BM id,BM code,MC name,JP spell,DZ address,DH telephone,LXR contact,CCTJ condition,BMBM department_id,");
                sb.Append("case when QY_FLAG='T' then 1 else 0 end is_use");
                return sb;
            }

            internal static readonly string fields = Build().ToString();
        }

    }
}
