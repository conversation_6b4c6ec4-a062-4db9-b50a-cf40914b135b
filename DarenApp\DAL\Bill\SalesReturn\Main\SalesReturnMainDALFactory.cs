﻿using DarenCore.Common;
using DarenCore.DAL;

namespace DarenApp.DAL.Bill.SalesReturn.Main
{
    public class SalesReturnMainDALFactory : IDALFactory<ISalesReturnMainDAL>
    {
        public ISalesReturnMainDAL Create(ErpType type)
        {
            return type switch
            {
                ErpType.DRUG => new DrugSalesReturnMainDAL(),
                _ => new InstrumentSalesReturnMainDAL(),
            };
        }
    }
}

