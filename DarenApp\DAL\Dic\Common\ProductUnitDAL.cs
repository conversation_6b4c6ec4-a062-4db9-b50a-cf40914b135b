﻿using DarenApp.Models.Dic;
using DarenCore.Common;
using DarenCore.DAL.OldVersion;
using DarenCore.DBUtils.SqlBuilder.Interfaces;
using System.Text;

namespace DarenApp.DAL.Dic.Common
{
    public class ProductUnitDAL : BaseDicDAL<ProductUnit>
    {
        public override string? GetTableName()
        {
            return "BM_JLDW";
        }

        public override Task<Result> SaveModelAsync(ProductUnit m, int _)
        {
            IInsertUpdateBuilder<ProductUnit> iu;
            if (m.is_new_row)
            {
                m.id = m.code;
                iu = CreateInsertBuilder();
                iu.AddItemStr("BM", m.code);
            }
            else
            {
                iu = CreateUpdateBuilder();
                iu.AddWhereItemString("BM", m.id);
            }
            iu.AddItemStr("MC", m.name);
            iu.AddItemStr("JP", m.spell);
            iu.AddItemBoolStr("QY_FLAG", m.is_use);
            return SaveModelAsync(iu, SaveDataType.DIC_CODE);
        }

        public override string GetLimit(ProductUnit m)
        {
            var sb = CreateStatementBuilder();
            sb.AddLikeBoth("BM", m.code);
            sb.AddLikeBoth("MC", m.name);
            sb.AddLikeBoth("JP", m.spell);
            if (!string.IsNullOrEmpty(m.search))
            {
                sb.AddLikeBoth("BM", m.search, null, "and (");
                sb.AddLikeBoth("MC", m.search, "or");
                sb.AddLikeBoth("JP", m.search, "or", null, ")");
            }
            sb.AddBoolStr("QY_FLAG", m.is_use);
            return sb.ToString();
        }

        public override string GetTableFields()
        {
            return FieldsBuilder.fields;
        }

        private class FieldsBuilder
        {
            private static StringBuilder Build()
            {
                var sb = new StringBuilder();
                sb.Append("BM id,BM code,MC name,JP spell,");
                sb.Append("case when QY_FLAG='T' then 1 else 0 end is_use");
                return sb;
            }

            internal static readonly string fields = Build().ToString();
        }


    }
}
