﻿using DarenCore.Common;
using DarenCore.Extension;
using DarenCore.Models.BaseModel;
using DarenCore.Models.Interfaces;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace DarenCore.Models
{
    /// <summary>
    /// 在线用户
    /// </summary>
    [Table("OnlineUser")]
    public class OnlineUser() : BaseIdModel, IAddDate
    {
        public OnlineUser(string? company_id) : this()
        {
            this.company_id = company_id ?? "0";
        }

        public OnlineUser(string? company_id = null, string? department_id = null, string? user_id = null, string? user_code = null, string? user_name = null) : this(company_id)
        {
            if (user_id != null) { this.user_id = user_id; }
            if (user_code != null) { this.user_code = user_code; }
            if (user_name != null) { this.user_name = user_name; }
            if (department_id != null) { this.department_id = department_id; }
        }

        public string user_id { get; set; }

        [DisplayName("员工编码")]
        public string user_code { get; set; }

        [DisplayName("员工名称")]
        public string user_name { get; set; }

        public string salt { get; set; }

        //角色ID
        public string role_id { get; set; }

        /// <summary>
        ///角色类型 1、管理人员(admin)  2、普通员工(employee)
        /// </summary>
        public int role_type { get; set; }

        //门店ID
        public string shop_id { get; set; }

        //部门ID
        public string department_id { get; set; }

        //默认仓库ID
        public string warehouse_id { get; set; }

        //默认仓库名称
        public string warehouse_name { get; set; }

        //门店权限
        public string power_shop { get; set; }

        //部门权限
        public string power_department { get; set; }

        //单据权限
        public string power_bill { get; set; }

        //菜单权限
        public string power_menu { get; set; }

        //其它权限
        public string power_other { get; set; }

        //查看成本权
        public int power_view_cost { get; set; }

        //查看所有部门库存权
        public int power_view_stock_all { get; set; }

        //查看所有菜品权
        public int power_view_dishes_all { get; set; }

        [DisplayName("企业ID")]
        public string company_id { get; set; }

        [DisplayName("设备ID")]
        public string? device_id { get; set; }

        [TypeConverter(typeof(EnumToNumberConverter<PlatformType, int>))]
        public UserType user_type { get; set; } = UserType.MANAGER;

        [TypeConverter(typeof(EnumToNumberConverter<PlatformType, int>))]
        public PlatformType platform_type { get; set; }

        [TypeConverter(typeof(EnumToNumberConverter<DbConfigType, int>))]
        public DbConfigType db_config_type { get; set; }


        [DisplayName("添加日期")]
        public DateTime? add_date { get; set; }

        //是否超级用户
        private int _is_admin = -1;

        [JsonIgnore]
        [NotMapped]
        public bool is_admin
        {
            get
            {
                if (_is_admin == -1)
                {
                    _is_admin = user_code.EqualsIgnoreCase("admin") || role_type == 1 ? 1 : 0;
                }
                return _is_admin == 1;
            }
            set 
            {
                _is_admin = value ? 1 : 0;
            }
        }

        [JsonIgnore]
        public bool is_super;
    }
}