﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <OutputType>Library</OutputType>
    <BaseOutputPath>E:\cursortest\dear\DarenMIS\Release\DarenApi</BaseOutputPath>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <GenerateAssemblyInfo>True</GenerateAssemblyInfo>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <GenerateAssemblyInfo>True</GenerateAssemblyInfo>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\DarenApp.Models\DarenApp.Models.csproj" />
    <ProjectReference Include="..\DarenApp\DarenApp.csproj" />
    <ProjectReference Include="..\DarenCore\DarenCore.csproj" />
    <ProjectReference Include="..\DarenWeb.Models\DarenWeb.Models.csproj" />
    <ProjectReference Include="..\DarenWeb\DarenWeb.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="ElemeSdk">
      <HintPath>..\Library\ElemeSdk.dll</HintPath>
    </Reference>
    <Reference Include="ShangouSdk">
      <HintPath>..\Library\ShangouSdk.dll</HintPath>
    </Reference>
    <Reference Include="TopSdk">
      <HintPath>..\Library\TopSdk.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>
