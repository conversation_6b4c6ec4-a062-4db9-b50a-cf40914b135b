﻿using DarenCore.Common;
using DarenCore.DAL;

namespace DarenApp.DAL.Bill.SalesOut.Main
{
    public class SalesOutMainDALFactory : IDALFactory<ISalesOutMainDAL>
    {
        public ISalesOutMainDAL Create(ErpType type)
        {
            return type switch
            {
                ErpType.DRUG => new DrugSalesOutMainDAL(),
                _ => new InstrumentSalesOutMainDAL(),
            };
        }
    }
}

