using DarenApp.Models.Bill.SalesOut;
using DarenCore.Common;
using DarenCore.DBUtils.SqlBuilder.Interfaces;
using System.Text;

namespace DarenApp.DAL.Bill.SalesOut.Product
{
    public class InstrumentSalesOutProductDAL : BaseSalesOutProductDAL
    {

        public override Task<Result> SaveModelAsync(SalesOutProduct m, int _)
        {
            IInsertUpdateBuilder<SalesOutProduct> iu;
            if (m.is_new_row)
            {
                iu = CreateInsertBuilder();
                iu.AddItemStr("ID", m.id);
                iu.AddItemInt("XH", m.sn);
            }
            else
            {
                iu = CreateUpdateBuilder();
                iu.AddWhereItemString("ID", m.id);
                iu.AddWhereItemInt("XH", m.sn);
            }

            iu.AddItemInt("NB", m.sn);
            iu.AddItemStr("SPBM", m.product_id);
            iu.AddItemStr("SPTM", m.product_barcode);
            iu.AddItemStr("SPMC", m.product_name);
            iu.AddItemStr("XHGG", m.specification);
            iu.AddItemStr("CLMC", m.material);
            iu.AddItemStr("SCCJ", m.manufacturer);
            iu.AddItemStr("ZCZH", m.registration_number);
            iu.AddItemInt("YXQX", m.expires_in);
            iu.AddItemStr("SCPH", m.batch_no);
            iu.AddItemDateTime("SCRQ", m.manufacture_date);
            iu.AddItemDateTime("YXQZ", m.expires_date);
            iu.AddItemStr("MJPH", m.sterilize_no);
            iu.AddItemDateTime("MJRQ", m.sterilize_date);
            iu.AddItemDateTime("MJXQ", m.sterilize_exp);
            iu.AddItemStr("PSID", m.serial_no);
            iu.AddItemStr("CB", m.cost_mode);
            iu.AddItemStr("JLDW", m.unit);
            iu.AddItemDecimal("SL", m.quantity);
            iu.AddItemDecimal("YJ", m.original_price);
            iu.AddItemDecimal("ZK", m.discount);
            iu.AddItemDecimal("DJ", m.price);
            iu.AddItemDecimal("YJ_HS", m.original_tax_price);
            iu.AddItemDecimal("RT", m.tax_rate);
            iu.AddItemDecimal("DJ_HS", m.tax_price);
            iu.AddItemDecimal("JE", m.amount);
            iu.AddItemDecimal("SE", m.tax);
            iu.AddItemDecimal("HJ", m.total);
            iu.AddItemDecimal("ZKJE", m.reduce);
            iu.AddItemDecimal("CBJJ", m.cost_price);
            iu.AddItemDecimal("XSCB", m.sales_cost);
            iu.AddItemDecimal("XSML", m.sales_profit);
            iu.AddItemDecimal("DJ_LSJ", m.retail_price);
            iu.AddItemDecimal("DJ_ZDJ", m.minimum_price);
            iu.AddItemDecimal("DJ_ZGJ", m.maximum_price);
            iu.AddItemStr("CKBM", m.warehouse_id);
            iu.AddItemStr("CKMC", m.warehouse_name);
            iu.AddItemStr("HWBH", m.location_no);
            iu.AddItemStr("BZ", m.note);
            iu.AddItemBoolStr("ZP", m.is_gift);
            iu.AddItemBoolStr("DZ", m.is_discount);
            iu.AddItemBoolStr("KB", m.is_bind);

            return SaveModelAsync(iu, SaveDataType.DEFAULT);
        }

        public override string GetTableFields()
        {
            return FieldsBuilder.fields;
        }

        private class FieldsBuilder
        {
            private static StringBuilder Build()
            {
                var sb = new StringBuilder();
                sb.Append("ID id,XH sn,ID pid,");
                sb.Append("SPBM product_id,SPBM product_code,SPMC product_name,");
                sb.Append("SPTM product_barcode,XHGG specification,CLMC material,SCCJ manufacturer,ZCZH registration_number,YXQX expires_in,");
                sb.Append("SCPH batch_no,SCRQ manufacture_date,YXQZ expires_date,MJPH sterilize_no,MJRQ sterilize_date,MJXQ sterilize_exp,PSID serial_no,CB cost_mode,JLDW unit,SL quantity,");
                sb.Append("YJ original_price,ZK discount,DJ price,YJ_HS original_tax_price,RT tax_rate,DJ_HS tax_price,JE amount,SE tax,HJ total,ZKJE reduce,");
                sb.Append("CBJJ cost_price,XSCB sales_cost,XSML sales_profit,");
                sb.Append("DJ_LSJ retail_price,DJ_ZDJ minimum_price,DJ_ZGJ maximum_price,");
                sb.Append("CKBM warehouse_id,CKMC warehouse_name,HWBH location_no,BZ note,");
                sb.Append("case when ZP='T' then 1 else 0 end is_gift,");
                sb.Append("case when DZ='T' then 1 else 0 end is_discount,");
                sb.Append("case when KB='T' then 1 else 0 end is_bind");
                return sb;
            }

            internal static readonly string fields = Build().ToString();
        }

    }

}
