﻿using DarenApp.DAL.Bill;
using DarenApp.Models.Bill;
using DarenCore.BLL.OldVersion;
using DarenCore.Common;
using DarenCore.Extension;
using DarenCore.Interface;
using DarenCore.Utils;

namespace DarenApp.BLL.Bill
{


    public class BillSnBLL : BaseBLL<BillSn, BillSnDAL>
    {

        public BillSnBLL()
        {

        }

        public BillSnBLL(IWebAppContext ctx) : base(ctx)
        {

        }

        public async Task<string> GetBillNo(string bill_type, string? salt = null)
        {
            var bill_date = DateUtils.CurrentDateStrSimple();
            var sn = await GetBillSn(bill_type, bill_date);
            if (string.IsNullOrEmpty(salt))
            {
                salt = user.user_id.Right(4);
            }
            return sn > 0 ? bill_date.Right(6) + salt + "30" + ("0000" + sn).Right(4) : "ErrorSN";
        }

        public async Task<int> GetBillSn(string bill_type, string bill_date)
        {
            try
            {
                var m =  await dal.GetModelAsync(user.user_id, bill_type, bill_date);
                if (m != null)
                {
                    return m.current_sn + 1;
                }
                else
                {
                    await dal.DeleteHistoryModel(user.user_id, bill_type);
                    var r = await dal.AddModelAsync(user.user_id, bill_type, bill_date, 0);
                    if (r.IsFail)
                    {
                        LogUtils.Error(r.message);
                        return -1;
                    }
                    return 1;
                }
            }
            catch(Exception ex) 
            {
                LogUtils.Error(ex);
                return -1;
            }
        }

        public async Task<Result> UpdateBillSn(string bill_type, string bill_no, string? bill_date = null)
        {
            try
            {
                var bill_sn = bill_no.Right(4).ToInt();
                if (bill_sn == 0)
                {
                    return Result.Fail();
                }
                if (string.IsNullOrEmpty(bill_date))
                {
                    bill_date = DateUtils.CurrentDateStrSimple();
                }
                var n = await dal.UpdateBillSn(user.user_id, bill_type, bill_date, bill_sn);
                if (n != 1)
                {
                    return Result.Fail($"受影响的行数为{n}");
                }
                return Result.Success();
            }
            catch (Exception ex)
            {
                return Result.Fail(ex);
            }
         }

    }
}