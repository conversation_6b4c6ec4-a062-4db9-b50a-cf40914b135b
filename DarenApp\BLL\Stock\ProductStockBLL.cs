﻿using DarenApp.DAL.Stock.ProductStockDAL;
using DarenApp.Models.Stock;
using DarenCore.BLL.OldVersion;
using DarenCore.Common;
using DarenCore.Interface;

namespace DarenApp.BLL.Stock
{
    public class ProductStockBLL : BaseBLL<ProductStock, IProductStockDAL>
    {
        public ProductStockBLL()
        {

        }

        public ProductStockBLL(IWebAppContext ctx) : base(ctx)
        {

        }

        public ProductStockBLL(HttpContext ctx, string company_id) : base(ctx, company_id)
        {

        }

        //public Result SaveModel(ProductStock m)
        //{
        //    var dal = GetDAL(out bool isCreator);

        //    try
        //    {
        //        var r = dal.SaveModel(m);
        //        return r;
        //    }
        //    catch (Exception ex)
        //    {
        //        return new Result(ResultCode.error, ex.Message);
        //    }
        //    finally
        //    {
        //        if (isCreator) base.CloseSqlHelper();
        //    }
        //}

        public async Task<(ProductStock?, Exception?)> GetModelAsync(ProductStock stock)
        {
            try
            {
                var m = await dal.GetModelAsync(stock);
                return (m, null);
            }
            catch (Exception ex)
            {
                return (null, ex);
            }
        }

        //public ProductStock GetModel8Id()
        //{
        //    return null;
        //}

        //public ProductStock GetModel8SerialNo(string department_id, string product_id, string serial_no)
        //{
        //    var dal = GetDAL(out bool isCreator);

        //    try
        //    {
        //        var m = dal.GetModel8SerialNo(department_id, product_id, serial_no);
        //        return m;
        //    }
        //    catch
        //    {
        //        return null;
        //    }
        //    finally
        //    {
        //        if (isCreator) base.CloseSqlHelper();
        //    }
        //}

        ////主要是SY类软件使用
        //public ProductStock GetModel8Department(string department_id, string warehouse_id, string product_id)
        //{
        //    var dal = GetDAL(out bool isCreator);

        //    try
        //    {
        //        var m = dal.GetModel8Department(department_id, warehouse_id, product_id);
        //        return m;
        //    }
        //    catch
        //    {
        //        return null;
        //    }
        //    finally
        //    {
        //        if (isCreator) base.CloseSqlHelper();
        //    }
        //}

        ////主要是CY类软件使用
        //public ProductStock GetModel8Shop(string shop_id, string warehouse_id, string product_id)
        //{
        //    var dal = GetDAL(out bool isCreator);

        //    try
        //    {
        //        var m = dal.GetModel8Shop(shop_id, warehouse_id, product_id);
        //        return m;
        //    }
        //    catch
        //    {
        //        return null;
        //    }
        //    finally
        //    {
        //        if (isCreator) base.CloseSqlHelper();
        //    }
        //}

        //public string GetPage(ProductStock m)
        //{
        //    var dal = GetDAL(out bool isCreator);

        //    try
        //    {
        //        var s = dal.GetPage(m);
        //        return s;
        //    }
        //    catch (Exception ex)
        //    {
        //        return new Result(ResultCode.error, ex.Message).ToJson();
        //    }
        //    finally
        //    {
        //        if (isCreator) base.CloseSqlHelper();
        //    }
        //}

        public async Task<Result> UpdateStockAsync(ProductStock m, int sn, bool isIncrease, bool isAudit)
        {
            try
            {
                return await dal.UpdateStockAsync(m, sn, isIncrease, isAudit, ctx.ParamOption);
            }
            catch (Exception ex)
            {
                return Result.Fail(ex);
            }
        }

        //public List<ProductStock> GetListForChoice(string department_id, string warehouse_id, string product_id)
        //{
        //    if (department_id.IsNullOrEmpty() || product_id.IsNullOrEmpty()) return null;

        //    var dal = GetDAL(out bool isCreator);

        //    try
        //    {
        //        department_id = DepartmentBLL.GetStockDepartmentId(user, department_id);
        //        var list = dal.GetList(department_id, warehouse_id, product_id);
        //        return list;
        //    }
        //    catch
        //    {
        //        return null;
        //    }
        //    finally
        //    {
        //        if (isCreator) base.CloseSqlHelper();
        //    }
        //}

        //public List<ProductStock> GetListForBatchNoChoice(string department_id, string warehouse_id, string product_id, string batch_no)
        //{
        //    var dal = GetDAL(out bool isCreator);

        //    try
        //    {
        //        department_id = DepartmentBLL.GetStockDepartmentId(user, department_id);
        //        var list = dal.GetListForBatchNoChoice(department_id, warehouse_id, product_id, batch_no);
        //        return list;
        //    }
        //    catch
        //    {
        //        return null;
        //    }
        //    finally
        //    {
        //        if (isCreator) base.CloseSqlHelper();
        //    }
        //}

        //public List<ProductStock> GetListForSerialNoChoice(string department_id, string warehouse_id, string product_id, string batch_no, string color_id)
        //{
        //    if (department_id.IsNullOrEmpty() || product_id.IsNullOrEmpty()) return null;

        //    var dal = GetDAL(out bool isCreator);

        //    try
        //    {
        //        department_id = DepartmentBLL.GetStockDepartmentId(user, department_id);
        //        var list = dal.GetListForSerialNoChoice(department_id, warehouse_id, product_id, batch_no, color_id);
        //        return list;
        //    }
        //    catch
        //    {
        //        return null;
        //    }
        //    finally
        //    {
        //        if (isCreator) base.CloseSqlHelper();
        //    }
        //}


        //public List<ProductStock> GetListForSizeChoice(string department_id, string warehouse_id, string product_id, string group_id, string color_id)
        //{
        //    if (department_id.IsNullOrEmpty() || product_id.IsNullOrEmpty()) return null;

        //    var dal = GetDAL(out bool isCreator);

        //    try
        //    {
        //        department_id = DepartmentBLL.GetStockDepartmentId(user, department_id);
        //        var list = dal.GetListForSizeChoice(department_id, warehouse_id, product_id, group_id, color_id);
        //        return list;
        //    }
        //    catch
        //    {
        //        return null;
        //    }
        //    finally
        //    {
        //        if (isCreator) base.CloseSqlHelper();
        //    }
        //}

        //public List<string> GetProductIdsForStoreSync(string department_id, string warehouse_id)
        //{
        //    if (department_id.IsNullOrEmpty()) return null;

        //    var dal = GetDAL(out bool isCreator);

        //    try
        //    {
        //        var list = dal.GetProductIdsForStoreSync(department_id, warehouse_id);
        //        return list;
        //    }
        //    catch (Exception ex)
        //    {
        //        Console.WriteLine(ex.Message);
        //        return null;
        //    }
        //    finally
        //    {
        //        if (isCreator) base.CloseSqlHelper();
        //    }
        //}




    }
}
