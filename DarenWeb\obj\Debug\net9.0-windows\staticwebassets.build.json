{"Version": 1, "Hash": "B0R006nqf5U4Lp/dvEV2dtAsoNkvfZgbA72JMqG/N5Y=", "Source": "DarenWeb", "BasePath": "_content/DarenWeb", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [{"Identity": "E:\\cursortest\\dear\\DarenCore\\DarenCore.csproj", "Version": 2, "Source": "DarenCore", "GetPublishAssetsTargets": "ComputeReferencedStaticWebAssetsPublishManifest;GetCurrentProjectPublishStaticWebAssetItems", "AdditionalPublishProperties": "Configuration=Debug;Platform=AnyCPU", "AdditionalPublishPropertiesToRemove": "WebPublishProfileFile;TargetFramework;RuntimeIdentifier;SelfContained", "GetBuildAssetsTargets": "GetCurrentProjectBuildStaticWebAssetItems", "AdditionalBuildProperties": "Configuration=Debug;Platform=AnyCPU", "AdditionalBuildPropertiesToRemove": "WebPublishProfileFile;TargetFramework;RuntimeIdentifier;SelfContained"}], "DiscoveryPatterns": [], "Assets": [], "Endpoints": []}