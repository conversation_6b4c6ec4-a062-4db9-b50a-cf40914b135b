<?xml version="1.0"?>
<doc>
    <assembly>
        <name>DarenWeb</name>
    </assembly>
    <members>
        <member name="T:DarenWeb.BLL.Dic.DistrictBLL">
            <summary>
            行政区划
            </summary>
        </member>
        <member name="M:DarenWeb.BLL.Dic.DistrictBLL.#ctor(DarenCore.Interface.IWebAppContext)">
            <summary>
            行政区划
            </summary>
        </member>
        <member name="M:DarenWeb.BLL.DL.DlDatabaseBLL.GetHostAsync(System.String)">
            <summary>
            计取主机
            </summary>
            <param name="host_id"></param>
            <returns></returns>
        </member>
        <member name="M:DarenWeb.BLL.DL.DlService.DropDatabaseAsync(System.String,System.String)">
            删除数据库
        </member>
        <member name="T:DarenWeb.BLL.OW.OwCourseBLL">
            <summary>
            培训教程
            </summary>
            <param name="ctx"></param>
        </member>
        <member name="M:DarenWeb.BLL.OW.OwCourseBLL.#ctor(DarenCore.Interface.IWebAppContext)">
            <summary>
            培训教程
            </summary>
            <param name="ctx"></param>
        </member>
        <member name="T:DarenWeb.BLL.OW.OwCourseCatalogBLL">
            <summary>
            软件教程目录
            </summary>
        </member>
        <member name="T:DarenWeb.BLL.OW.OwCustomerBLL">
            <summary>
            客户信息
            </summary>
        </member>
        <member name="M:DarenWeb.BLL.OW.OwService.GetConfig(System.String,System.Boolean,System.Boolean,System.String)">
            <summary>
            获取官网配置信息
            </summary>
            <param name="navTitle">导航标题</param>
            <param name="isHomePage">是否首页</param>
            <param name="showBreadcrumb">是否显示面包屑导航</param>
            <param name="keywords">关键词</param>
            <returns></returns>
        </member>
        <member name="M:DarenWeb.BLL.OW.OwService.GetOnlineQQList">
            <summary>
             随机读取客服QQ
            </summary>
        </member>
        <member name="M:DarenWeb.BLL.OW.OwService.GetMobilePhone">
            <summary>
             随机读取热线电话
            </summary>
        </member>
        <member name="T:DarenWeb.BLL.OW.OwSoftCategoryBLL">
            <summary>
            软件产品类别
            </summary>
        </member>
        <member name="F:DarenWeb.Common.Constant.CACHE_ONLINE_QQ_LIST">
            <summary>
            随机客服QQ列表cache
            </summary>
        </member>
        <member name="F:DarenWeb.Common.Constant.CACHE_MOBILE_PHONE">
            <summary>
            随机移动电话cache
            </summary>
        </member>
        <member name="F:DarenWeb.Common.Constant.CACHE_COURSE_CATALOG">
            <summary>
            教程目录cache
            </summary>
        </member>
        <member name="F:DarenWeb.Common.Constant.CACHE_ACCESS_TOKEN">
            <summary>
            API令牌cache
            </summary>
        </member>
        <member name="F:DarenWeb.Common.Constant.CACHE_NAVIGATION_LIST">
            <summary>
            导航菜单cache
            </summary>
        </member>
        <member name="F:DarenWeb.Common.Constant.SYSTEM_COMPANY_ID">
            <summary>
            系统企业ID
            </summary>
        </member>
        <member name="T:DarenWeb.Common.WebApp">
            <summary>
            基础WebApp
            </summary>
            <param name="options"></param>
        </member>
        <member name="M:DarenWeb.Common.WebApp.#ctor(DarenCore.Common.AppOptions)">
            <summary>
            基础WebApp
            </summary>
            <param name="options"></param>
        </member>
        <member name="T:DarenWeb.Common.WebAppMaster">
            <summary>
            主WebApp
            </summary>
            <param name="options"></param>
        </member>
        <member name="M:DarenWeb.Common.WebAppMaster.#ctor(DarenCore.Common.AppOptions)">
            <summary>
            主WebApp
            </summary>
            <param name="options"></param>
        </member>
        <member name="P:DarenWeb.Controllers.BaseController.ctx">
            <summary>
            根据当前用户信息获取WebApp应用上下文。
            </summary>
            <returns></returns>
        </member>
        <member name="M:DarenWeb.Controllers.BaseController.GetContextByCurrentUser">
            <summary>
            根据当前用户信息获取Web应用上下文，创建数据库上下文。
            </summary>
            <returns></returns>
        </member>
        <member name="M:DarenWeb.Controllers.BaseController.ContextByDefaultUser">
            <summary>
            根据当前用户信息获取Web应用上下文，创建数据库上下文。
            </summary>
            <returns></returns>
        </member>
        <member name="T:DarenWeb.Controllers.Common.CommonController">
            <summary>
            Common控制器
            </summary>
        </member>
        <member name="T:DarenWeb.Controllers.Common.HomeController">
            <summary>
            入口(索引)控制器
            </summary>
        </member>
        <member name="M:DarenWeb.Controllers.Common.HomeController.Login(System.String,System.String,System.String,System.String)">
            <summary>
            用户登录
            </summary>
            <param name="user_code">用户编码</param>
            <param name="password">用户密码</param>
            <param name="company_code">公司编码</param>
            <param name="platform_type">平台类型</param>
            <returns></returns>
        </member>
        <member name="T:DarenWeb.Controllers.Dic.EmployeeController">
            <summary>
            员工信息
            </summary>
        </member>
        <member name="M:DarenWeb.Controllers.Dic.EmployeeController.AddAdmin(System.String)">
            <summary>
            为指定企业添加默认系统管理员
            </summary>
            <param name="company_id"></param>
            <returns></returns>
        </member>
        <member name="M:DarenWeb.Controllers.Dic.EmployeeController.ResetAdminPassword(System.String)">
            <summary>
            重置指定企业的管理员密码
            </summary>
            <param name="company_id"></param>
            <returns></returns>
        </member>
        <member name="T:DarenWeb.Controllers.OwController">
            <summary>
            官网控制器 - OW是OfficialWeb的简写
            </summary>
        </member>
        <member name="P:DarenWeb.Controllers.OwController.webctx">
            <summary>
            根据系统默认的WEB用户获取Web应用上下文
            </summary>
        </member>
        <member name="M:DarenWeb.Controllers.OwController.GetContext">
            <summary>
            根据系统默认的WEB用户获取Web应用上下文，创建数据库上下文
            </summary>
            <returns></returns>
        </member>
        <member name="M:DarenWeb.Controllers.OwController.Init(System.String,System.Boolean,System.Boolean,System.String,System.String)">
            <summary>
            初始化配置信息
            </summary>
            <param name="navTitle">导航标题</param>
            <param name="isHomePage">是否首页</param>
            <param name="showBreadcrumb">是否显示面包屑导航条</param>
            <param name="keywords">关键词</param>
            <param name="type">访问来源</param>
        </member>
        <member name="M:DarenWeb.Controllers.OwController.Product(System.String)">
            <summary>
            产品中心
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:DarenWeb.Controllers.OwController.GetSoftPage(System.String,System.String,System.Int32,System.Int32)">
            <summary>
            获取软件分页数据
            </summary>
            <param name="category_id">产品类别</param>
            <param name="type">访问来源(如：百度、搜狗及搜搜等)</param>
            <param name="pageIndex">页码</param>
            <param name="pageSize">每页行数</param>
            <returns></returns>
        </member>
        <member name="M:DarenWeb.Controllers.OwController.SoftIntroduce(System.String,System.String)">
            <summary>
            软件介绍
            </summary>
            <param name="id">软件ID</param>
            <param name="type">访问来源(如：百度、搜狗及搜搜等)</param>
            <returns></returns>
        </member>
        <member name="M:DarenWeb.Controllers.OwController.SoftDownload(System.String,System.Int32,System.String)">
            <summary>
            软件下载
            </summary>
            <param name="id">软件ID</param>
            <param name="website_index"></param>
            <param name="path_package"></param>
            <returns></returns>
        </member>
        <member name="M:DarenWeb.Controllers.OwController.DownloadApp(System.String)">
            <summary>
            APP下载
            </summary>
            <param name="fileName"></param>
            <returns></returns>
        </member>
        <member name="M:DarenWeb.Controllers.OwController.Case">
            <summary>
            成功案例
            </summary>
            <returns></returns>
        </member>
        <member name="M:DarenWeb.Controllers.OwController.GetCasePage(System.String,System.String,System.String,System.Int32,System.Int32)">
            <summary>
            获取成功案例分页数据
            </summary>
            <param name="province">省/直辖市</param>
            <param name="city">市/区</param>
            <param name="country">区/县</param>
            <param name="pageIndex">页码</param>
            <param name="pageSize">每页行数</param>
            <returns></returns>
        </member>
        <member name="M:DarenWeb.Controllers.OwController.GetDistrictTree(System.String)">
            <summary>
            行政区划
            </summary>
            <param name="pid">行政区划父ID</param>
            <returns></returns>
        </member>
        <member name="M:DarenWeb.Controllers.OwController.Agent">
            <summary>
            合作伙伴
            </summary>
            <returns></returns>
        </member>
        <member name="M:DarenWeb.Controllers.OwController.Service">
            <summary>
            售后服务
            </summary>
            <returns></returns>
        </member>
        <member name="M:DarenWeb.Controllers.OwController.GetCourseCatalogTree(System.String,System.String)">
            <summary>
            培训教程目录树
            </summary>
            <param name="pid">培训教程目录父ID</param>
            <param name="soft_category_id">软件类别ID</param>
            <returns></returns>
        </member>
        <member name="M:DarenWeb.Controllers.OwController.GetCourses(System.String)">
            <summary>
            培训教程
            </summary>
            <param name="catalog_id">培训教程目录ID</param>
            <returns></returns>
        </member>
        <member name="M:DarenWeb.Controllers.OwController.About">
            <summary>
            关于我们
            </summary>
            <returns></returns>
        </member>
        <member name="M:DarenWeb.Controllers.OwController.GetDic(System.String,System.String,System.String)">
            <summary>
            读取数据字典
            </summary>
            <param name="dicName">字典名称(与表名一致)</param>
            <param name="pid">父ID</param>
            <param name="orderby">排序方式</param>
            <returns></returns>
        </member>
        <member name="M:DarenWeb.Controllers.OwController.GenReg(System.String)">
            <summary>
            软件注册
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:DarenWeb.Controllers.OwController.GenRegCode(System.String,System.String,System.String,System.String)">
            <summary>
            生成注册码
            </summary>
            <param name="id"></param>
            <param name="dog_no"></param>
            <param name="dog_id"></param>
            <param name="usr_id"></param>
            <returns></returns>
        </member>
        <member name="M:DarenWeb.Controllers.OwController.OptimizePageSize(System.Int32)">
            <summary>
            优化每页行数
            </summary>
            <param name="pageSize"></param>
            <returns></returns>
        </member>
        <member name="T:DarenWeb.Controllers.Sys.CompanyController">
            <summary>
            企业信息
            </summary>
        </member>
        <member name="T:DarenWeb.Controllers.Sys.NavigationController">
            <summary>
            系统导航
            </summary>
        </member>
        <member name="M:DarenWeb.DAL.Sys.SysLogDAL.GetModelAsync(System.String,System.Int32,System.String,System.String)">
            <summary>
            根据用户ID返回上一次登录记录
            </summary>
        </member>
        <member name="M:DarenWeb.DAL.Sys.SysNavigationDAL.GetListAsync(System.String)">
            <summary>
            取得所有导航列表
            </summary>
            <param name="pid">父ID，值为null时获取所有类别</param>
            <returns>List</returns>
        </member>
        <member name="M:DarenWeb.DAL.Sys.SysNavigationDAL.GetPageAsync(System.String,System.String,System.Int32,System.Int32)">
             <summary>
            获取后台导航菜单分页json
             </summary>
             <param name="pid">父ID，值为null时获取所有类别</param>
             <param name="search"></param>
             <param name="pageIndex"></param>
             <param name="pageSize"></param>
             <returns>string</returns>
        </member>
        <member name="M:DarenWeb.DAL.Sys.SysNavigationDAL.GetTreeJsonAsync(System.String)">
             <summary>
            获取后台导航菜单分页json
             </summary>
             <param name="pid">父ID</param>
        </member>
        <member name="T:DarenWeb.Services.BackupService">
            <summary>
            定时备份数据的后台服务
            </summary>
        </member>
    </members>
</doc>
