﻿using DarenCore.BLL.OldVersion;
using DarenCore.Common;
using DarenCore.DAL.OldVersion;
using DarenCore.Interface;

namespace DarenApp.BLL.Bill
{
    public class BillMainBaseBLL<M,D> : BaseBLL<M, D> where M : class, new() where D : IBaseBillMainDAL<M>
    {
        public BillMainBaseBLL(IWebAppContext ctx) : base(ctx)
        {

        }

        public BillMainBaseBLL(HttpContext ctx, string company_id) : base(ctx, company_id)
        {

        }

        //根据单号查找单据是否存在
        public async Task<bool> ExistsByBillNoAsync(string id)
        {
            try
            {
                return await dal.ExistsByBillNoAsync(id);
            }
            catch
            {
                return false;
            }
        }

        //获取单据状态
        public async Task<int> GetBillStatusAsync(string id)
        {
            try
            {
                return await dal.GetBillStatusAsync(id);
            }
            catch
            {
                return -1;
            }
        }

        //更新单据状态
        public async Task<Result> UpdateBillStatusAsync(string id, int status, string? auditor_id, string? auditor_name, DateTime? audit_date)
        {
            try
            {
                var c = await dal.UpdateBillStatusAsync(id, status, auditor_id, auditor_name, audit_date);
                return c > 0 ? Result.Success() : Result.Fail("单据不存在");
            }
            catch (Exception ex)
            {
                return Result.Fail(ex);
            }
        }

 
    }
}
