﻿using DarenApp.DAL.Bill.SalesReturn.Product;
using DarenApp.Models.Bill.SalesReturn;
using DarenCore.BLL.OldVersion;
using DarenCore.Common;
using DarenCore.Interface;

namespace DarenApp.BLL.Bill.SalesReturn
{
    public class SalesReturnProductBLL(IWebAppContext ctx) : BaseBLL<SalesReturnProduct, ISalesReturnProductDAL>(ctx)
    {
        public async Task<Result> SaveModelsAsync(List<SalesReturnProduct> list, string pid)
        {
            var i = 0;
            foreach (var m in list)
            {
                i++;
                if (m.is_new_row)
                {
                    m.SetId(GetBillId(pid, i));
                }
                m.sn = i;
                m.pid = pid;
            }

            try
            {
                await DbHelper.BeginTransAsync();
                var r = await dal.SaveModelsAsync(list);
                if (r.IsSuccess)
                {
                    await DbHelper.CommitAsync();
                }
                else
                {
                    await DbHelper.RollbackAsync();
                }
                return r;
            }
            catch (Exception ex)
            {
                return Result.Fail(ex);
            }
        }
    }
}
