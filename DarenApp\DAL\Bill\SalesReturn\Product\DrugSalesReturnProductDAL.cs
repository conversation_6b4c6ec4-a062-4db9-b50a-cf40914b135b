using DarenApp.Models.Bill.SalesReturn;
using DarenCore.Common;
using DarenCore.DAL.OldVersion;
using DarenCore.DBUtils.SqlBuilder.Interfaces;
using System.Text;

namespace DarenApp.DAL.Bill.SalesReturn.Product
{
    public class DrugSalesReturnProductDAL : BaseSalesReturnProductDAL
    {

        public override Task<Result> SaveModelAsync(SalesReturnProduct m, int _)
        {
            IInsertUpdateBuilder<SalesReturnProduct> iu;

            if (m.is_new_row)
            {
                iu = CreateInsertBuilder();
                iu.AddItemStr("ID", m.id);
                iu.AddItemInt("XH", m.sn);
            }
            else
            {
                iu = CreateUpdateBuilder();
                iu.AddWhereItemString("ID", m.id);
                iu.AddWhereItemInt("XH", m.sn);
            }

            iu.AddItemInt("NB", m.sn);
            iu.AddItemStr("YPBM", m.product_id);
            iu.AddItemStr("YPTM", m.product_barcode);
            iu.AddItemStr("YPMC", m.product_name);
            iu.AddItemStr("YPGG", m.specification);
            iu.AddItemStr("JXMC", m.form);
            iu.AddItemStr("CDMC", m.growth);
            iu.AddItemStr("SCCJ", m.manufacturer);
            iu.AddItemStr("PZWH", m.approval_number);
            iu.AddItemInt("YXQX", m.expires_in);
            iu.AddItemStr("SCPH", m.batch_no);
            iu.AddItemDateTime("SCRQ", m.manufacture_date);
            iu.AddItemDateTime("YXQZ", m.expires_date);
            iu.AddItemStr("CB", m.cost_mode);
            iu.AddItemStr("JLDW", m.unit);
            iu.AddItemDecimal("SL", m.quantity);
            iu.AddItemDecimal("XS", m.quantity);
            iu.AddItemDecimal("YJ", m.original_price);
            iu.AddItemDecimal("ZK", m.discount);
            iu.AddItemDecimal("DJ", m.price);
            iu.AddItemDecimal("YJ_HS", m.original_tax_price);
            iu.AddItemDecimal("RT", m.tax_rate);
            iu.AddItemDecimal("DJ_HS", m.tax_price);
            iu.AddItemDecimal("JE", m.amount);
            iu.AddItemDecimal("SE", m.tax);
            iu.AddItemDecimal("HJ", m.total);
            iu.AddItemDecimal("ZKJE", m.reduce);
            iu.AddItemDecimal("CBJJ", m.cost_price);
            iu.AddItemDecimal("XSCB", m.sales_cost);
            iu.AddItemDecimal("XSML", m.sales_profit);
            iu.AddItemStr("JFGZ", m.bonus_rule);
            iu.AddItemStr("CKBM", m.warehouse_id);
            iu.AddItemStr("CKMC", m.warehouse_name);
            iu.AddItemStr("HWBH", m.location_no);
            iu.AddItemBoolStr("JF", m.is_bonus);
            iu.AddItemBoolStr("ZP", m.is_gift);
            iu.AddItemBoolStr("DZ", m.is_discount);

            return SaveModelAsync(iu, SaveDataType.DEFAULT);
        }

        public override string GetTableFields()
        {
            return FieldsBuilder.fields;
        }

        private class FieldsBuilder
        {
            private static StringBuilder Build()
            {
                var sb = new StringBuilder();
                sb.Append("ID id,XH sn,ID pid,");
                sb.Append("YPBM product_id,YPBM product_code,YPTM product_barcode,YPMC product_name,");
                sb.Append("YPGG specification,JXMC form,CDMC growth,SCCJ manufacturer,PZWH approval_number,YXQX expires_in,");
                sb.Append("SCPH batch_no,SCRQ manufacture_date,YXQZ expires_date,CB cost_mode,JLDW unit,SL quantity,");
                sb.Append("YJ original_price,ZK discount,DJ price,YJ_HS original_tax_price,RT tax_rate,DJ_HS tax_price,JE amount,SE tax,HJ total,ZKJE reduce,");
                sb.Append("CBJJ cost_price,XSCB sales_cost,XSML sales_profit,");
                sb.Append("JFGZ bonus_rule,CKBM warehouse_id,CKMC warehouse_name,HWBH location_no,");
                sb.Append("case when JF='T' then 1 else 0 end is_bonus,");
                sb.Append("case when ZP='T' then 1 else 0 end is_gift,");
                sb.Append("case when DZ='T' then 1 else 0 end is_discount");
                return sb;
            }

            internal static readonly string fields = Build().ToString();
        }

    }

}
