﻿using DarenApp.BLL.Sys;
using DarenApp.DAL.Dic.Enterprisex.Supplierx;
using DarenApp.Models.Dic;
using DarenCore.BLL;
using DarenCore.BLL.OldVersion;
using DarenCore.Common;
using DarenCore.Extension;
using DarenCore.Interface;
using DarenCore.Utils;

namespace DarenApp.BLL.Dic
{
    public class SupplierBLL : BaseDicBLL<Supplier, ISupplierDAL>, IControllerDicBLL<Supplier>
    {
        public SupplierBLL()
        {

        }

        public SupplierBLL(IWebAppContext ctx) : base(ctx)
        {

        }

        public SupplierBLL(HttpContext ctx, string company_id) : base(ctx, company_id)
        {

        }

        public override async Task<(string key, string id, object? data, Exception?)> SaveModelAsync(Supplier m, int sn)
        {
            if (m.is_new_row)
            {
                m.registration_date = DateUtils.CurrentDateTime();
            }

            if (string.IsNullOrEmpty(m.spell))
            {
                m.spell = GetFirstSpell(m.name);
            }

            try
            {
                await dal.SaveModelAsync(m, sn);
                return (m.key, m.id, m.spell, null);
            }
            catch (Exception ex)
            {
                return ("", "", null, ex);
            }
        }

        public async Task<Result> DeleteAsync(string id)
        {
            if (id.IsNullOrEmpty())
            {
                return Result.Fail("参数错误！");
            }

            var r = await new TableService(ctx).CheckDicUseStatus1Aysnc("DWBM", id, "供货方");
            if (r.IsSuccess)
            {
                try
                {
                    await dal.DeleteByIdAsync(id, 1);
                }
                catch (Exception ex)
                {
                    return Result.Fail(ex);
                }
            }
            return r;
        }

        public async Task<Result> DeletesAsync(string ids)
        {
            if (ids.IsNullOrEmpty())
            {
                return Result.Fail("参数错误！");
            }

            var r = await new TableService(ctx).CheckDicUseStatus2Aysnc("DWBM", ids, "供货方");
            if (r.IsSuccess)
            {
                try
                {
                    await dal.DeleteByIdsAsync(ids, 1);
                }
                catch (Exception ex)
                {
                    return Result.Fail(ex);
                }
            }
            return r;
        }

        public async Task<Result> GetMaxCodeAsync(string category_id)
        {
            var (code, ex) = await GetMaxCodeAsync(4, "LB", category_id);
            return Result.ByException(ex, code);
        }

        /// <summary>
        /// 更新往来余额
        /// </summary>
        /// <param name="id"></param>
        /// <param name="amount"></param>
        /// <param name="type"></param>
        /// <param name="isIncrease"></param>
        /// <returns></returns>
        public async Task<Result> UpdateCurrentBalanceAsync(string id, decimal amount, string type, bool isIncrease)
        {
            try
            {
                var (n, ex) = await dal.UpdateCurrentBalanceAsync(id, amount, type, isIncrease);
                if (ex != null)
                {
                    return Result.Fail(ex);
                }
                if (n != 1)
                {
                    return Result.Fail($"更新的行数为{n}");
                }
                return Result.Success();
            }
            catch (Exception ex)
            {
                return Result.Fail(ex);
            }
        }


    }
}
