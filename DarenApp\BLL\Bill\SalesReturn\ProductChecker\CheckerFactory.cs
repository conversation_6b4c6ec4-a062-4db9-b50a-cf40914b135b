﻿using Climber.Model;
using DarenApp.Models.Bill.SalesReturn;
using DarenCore.Common;

namespace DarenApp.BLL.Bill.SalesReturn.ProductChecker
{
    public class CheckerFactory
    {

        public static IBillProductChecker<SalesReturnMain, SalesReturnProduct> Get(ErpType erpType)
        {
            return erpType switch
            {
                ErpType.DRUG or ErpType.INSTRUMENT => new DrugChecker(),
                _ => new CommonChecker(),
            };
        }

    }
}
