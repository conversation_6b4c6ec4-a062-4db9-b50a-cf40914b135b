﻿using DarenApp.DAL.Dic.Productx;
using DarenApp.Models.Dic;
using DarenCore.BLL;
using DarenCore.BLL.OldVersion;
using DarenCore.Common;
using DarenCore.Interface;
using Newtonsoft.Json.Linq;

namespace DarenApp.BLL.Dic
{
    public class ProductBLL : BaseDicBLL<Product, IProductDAL>, IControllerDicBLL<Product>
    {
        private readonly IWebAppContext _ctx;

        public ProductBLL()
        {

        }

        public ProductBLL(IWebAppContext ctx) : base(ctx)
        {
            _ctx = ctx;
        }

        public ProductBLL(HttpContext httpctx, string company_id)
        {
            _ctx = WebAppContext.CreateAppContext_UseBook(httpctx, false, company_id);
            base.SetContext(_ctx);
        }

        //public object PrepareData(Product m, int index)
        //{
        //    if (m.id.IsNullOrEmpty())
        //    {
        //        m.id = base.GetDicId(m.code, index);
        //        m.add_date = DateUtils.CurrentDateTime();
        //        m.is_new_row = true;
        //    }
        //    else
        //    {
        //        m.edit_date = DateUtils.CurrentDateTime();
        //    }

        //    if (m.spell.IsNullOrEmpty())
        //    {
        //        m.spell = ComUtils.GetFirstSpell(m.name);
        //        return new { m.key, m.id, m.spell };
        //    }
        //    else
        //    {
        //        return new { m.key, m.id };
        //    }
        //}

        //public Result SaveModels(List<Product> list)
        //{
        //    if (list == null || list.Count == 0)
        //    {
        //        return new Result(ResultCode.ok);
        //    }

        //    var i = 0;
        //    var items = new List<object>();
        //    foreach (var m in list)
        //    {
        //        i++;
        //        items.Add(PrepareData(m, i));
        //    }

        //    var dal = GetDAL(out bool isCreator);
        //    try
        //    {
        //        var r = dal.SaveModels(list);
        //        if (r.ok)
        //        {
        //            r.retdata = items;
        //        }
        //        return r;
        //    }
        //    catch (Exception ex)
        //    {
        //        return new Result(ResultCode.error, ex.Message);
        //    }
        //    finally
        //    {
        //        if (isCreator) base.CloseSqlHelper();
        //    }
        //}

        //public Result SaveModel(Product m)
        //{
        //    var item = PrepareData(m, 1);

        //    var dal = GetDAL(out bool isCreator);
        //    try
        //    {
        //        var r = dal.SaveModel(m);
        //        if (r.ok)
        //        {
        //            r.retdata = item;
        //        }
        //        return r;
        //    }
        //    finally
        //    {
        //        if (isCreator) base.CloseSqlHelper();
        //    }
        //}

        //public Result SaveModelEx(Product m, ProductExtend e)
        //{
        //    var factory = base.GetDalFactory(out bool isCreator);

        //    try
        //    {
        //        SqlHelper.BeginTrans();

        //        var r1 = SaveModel(m);
        //        if (r1.error)
        //        {
        //            SqlHelper.RollbackTrans();
        //            return r1;
        //        }

        //        var r2 = new ProductExtendBLL(user, factory).SaveModel(e, m.id);
        //        if (r2.error)
        //        {
        //            SqlHelper.RollbackTrans();
        //            return r2;
        //        }

        //        try
        //        {
        //            SqlHelper.CommitTrans();
        //            return new Result(ResultCode.ok, null, new
        //            {
        //                baseInfo = r1.retdata,
        //                extendInfo = r2.retdata
        //            });
        //        }
        //        catch (Exception ex)
        //        {
        //            SqlHelper.RollbackTrans();
        //            return new Result(ResultCode.error, "保存失败,原因：" + ex.Message);
        //        }
        //    }
        //    finally
        //    {
        //        if (isCreator) base.CloseSqlHelper();
        //    }
        //}

        //public Product GetModel(string id)
        //{
        //    return base.GetModel<Product>(id);
        //}

        //public object GetModelEx(string id)
        //{
        //    var factory = base.GetDalFactory(out bool isCreator);
        //    try
        //    {
        //        return new
        //        {
        //            baseInfo = GetModel(id),
        //            extendInfo = new ProductExtendBLL(user, factory).GetModel(id)
        //        };
        //    }
        //    finally
        //    {
        //        if (isCreator) base.CloseSqlHelper();
        //    }
        //}

        //public Product GetModel8Id(string id)
        //{
        //    return base.GetModel8Code<Product>(id);
        //}

        //public Product GetModel8Code(string id)
        //{
        //    return base.GetModel8Code<Product>(id);
        //}

        //public Product GetModel8Barcode(string barcode)
        //{
        //    var dal = GetDAL(out bool isCreator);

        //    try
        //    {
        //        var m = dal.GetModel8Barcode(barcode);
        //        return m;
        //    }
        //    catch
        //    {
        //        return null;
        //    }
        //    finally
        //    {
        //        if (isCreator) base.CloseSqlHelper();
        //    }
        //}

        //public decimal GetPrice(string product_id, string price_type)
        //{
        //    var dal = GetDAL(out bool isCreator);

        //    try
        //    {
        //        if (bookConfig.erp_version_type == ERPVersionType.NEW)
        //        {
        //            return dal.GetPrice(product_id, price_type);
        //        }
        //        else
        //        {
        //            return dal.GetPrice(product_id, ComConvert.GetPriceType(price_type));
        //        }
        //    }
        //    catch
        //    {
        //        return 0;
        //    }
        //    finally
        //    {
        //        if (isCreator) base.CloseSqlHelper();
        //    }
        //}

        //public string GetMaxCode(string category_id)
        //{
        //    return base.GetMaxCode(4, "LB", category_id);
        //}

        //public List<Product> GetList(Product m)
        //{
        //    var dal = GetDAL(out bool isCreator);

        //    try
        //    {
        //        var list = dal.GetList(m);
        //        return list;
        //    }
        //    catch
        //    {
        //        return null;
        //    }
        //    finally
        //    {
        //        if (isCreator) base.CloseSqlHelper();
        //    }
        //}

        //public string GetPage(Product m)
        //{
        //    var dal = GetDAL(out bool isCreator);

        //    try
        //    {
        //        var s = dal.GetPage(m);
        //        return s;
        //    }
        //    finally
        //    {
        //        if (isCreator) base.CloseSqlHelper();
        //    }
        //}

        //public string GetStockPage(Product m, string department_id, string warehouse_id)
        //{
        //    var dal = GetDAL(out bool isCreator);

        //    try
        //    {
        //        department_id = DepartmentBLL.GetStockDepartmentId(user, department_id);
        //        var s = dal.GetStockPage(m, department_id, warehouse_id);
        //        return s;
        //    }
        //    finally
        //    {
        //        if (isCreator) base.CloseSqlHelper();
        //    }
        //}

        //public Result DeleteModel(string id)
        //{
        //    var r = new TableBLL(user).CheckDicUseStatus1(bookConfig.erp_type == ERPType.DRUG ? "YPBM" : "SPBM", id, "商品编码");
        //    if (r.ok)
        //    {
        //        if (bookConfig.erp_version_type == ERPVersionType.NEW)
        //        {
        //            r = base.DeleteModel8Id(id);
        //        }
        //        else
        //        {
        //            r = base.DeleteModel8Code(id);
        //        }
        //    }
        //    return r;
        //}

        //public Result DeleteModels(string ids)
        //{
        //    var r = new TableBLL(user).CheckDicUseStatus2(bookConfig.erp_type == ERPType.DRUG ? "YPBM" : "SPBM", ids, "商品编码");
        //    if (r.ok)
        //    {
        //        if (bookConfig.erp_version_type == ERPVersionType.NEW)
        //        {
        //            r = base.DeleteModel8Ids(ids);
        //        }
        //        else
        //        {
        //            r = base.DeleteModel8Codes(ids);
        //        }
        //    }
        //    return r;
        //}

        ////更新使用状态
        //public Result UpdateIsUse(string id, int value)
        //{
        //    if (bookConfig.erp_version_type == ERPVersionType.NEW)
        //    {
        //        return base.UpdateIsUse8Id(id, value);
        //    }
        //    else
        //    {
        //        return base.UpdateIsUse8Code(id, value);
        //    }
        //}

        ////更新提成信息
        //public Result UpdateCommissionInfo()
        //{
        //    var factory = base.GetDalFactory(out bool isCreator);

        //    try
        //    {

        //        var list = new ProductMarketingBLL(user, factory).GetList(new ProductMarketing());

        //        Result r;

        //        foreach (var item in list)
        //        {
        //            r = this.UpdateCommissionInfo(item);
        //            if (r.error)
        //            {
        //                SqlHelper.RollbackTrans();
        //                return r;
        //            }
        //        }

        //        return new Result(ResultCode.ok);
        //    }
        //    finally
        //    {
        //        if (isCreator) base.CloseSqlHelper();
        //    }
        //}

        ////更新会员级别信息
        //public Result UpdateCommissionInfo(ProductMarketing m)
        //{
        //    var dal = GetDAL(out bool isCreator);

        //    try
        //    {
        //        var r = dal.UpdateCommissionInfo(m);
        //        return r;
        //    }
        //    catch (Exception ex)
        //    {
        //        return new Result(ResultCode.error, ex.Message);
        //    }
        //    finally
        //    {
        //        if (isCreator) base.CloseSqlHelper();
        //    }
        //}

        ////获取售价
        //public List<ProductSalesPrice> GetProductSalesPrice(string department_id, string price_type, string product_ids)
        //{
        //    return GetProductSalesPrice(department_id, price_type, (product_ids ?? "").Split('|'));
        //}

        //public List<ProductSalesPrice> GetProductSalesPrice(string department_id, string price_type, string[] product_ids)
        //{
        //    var factory = base.GetDalFactory(out bool isCreator);

        //    try
        //    {
        //        ProductPriceBLL priceBLL = null;
        //        var promotionBLL = new StockPromotionMainBLL(user, factory);
        //        var list = new List<ProductSalesPrice>();

        //        foreach (var product_id in product_ids)
        //        {
        //            //提取促销价
        //            var price = promotionBLL.GetPrice(department_id, product_id, price_type);

        //            //提取部门价
        //            if (price == 0 && ComJudge.ProductIsOwnDepartmentPrice(bookConfig.erp_type))
        //            {
        //                if (priceBLL == null)
        //                {
        //                    priceBLL = new ProductPriceBLL(user, factory);
        //                }
        //                price = priceBLL.GetPrice(department_id, product_id, price_type);
        //            }

        //            //提取商品价
        //            if (price == 0)
        //            {
        //                price = GetPrice(product_id, price_type);
        //                if (price == 0 && price_type != "retail_price")
        //                {
        //                    price = GetPrice(product_id, "retail_price");  //提取零售价
        //                }
        //            }

        //            list.Add(new ProductSalesPrice()
        //            {
        //                id = product_id,
        //                price = price,
        //            });
        //        }

        //        return list;
        //    }
        //    finally
        //    {
        //        if (isCreator) base.CloseSqlHelper();
        //    }
        //}

        ////获取售价和库存信息
        //public List<ProductPriceStock> GetProductPriceStocks(string department_id, string price_type, string product_ids)
        //{
        //    return GetProductPriceStocks(department_id, price_type, (product_ids ?? "").Split('|'));
        //}

        //public List<ProductPriceStock> GetProductPriceStocks(string department_id, string price_type, string[] product_ids)
        //{
        //    var factory = base.GetDalFactory(out bool isCreator);

        //    try
        //    {
        //        ProductPriceBLL priceBLL = null;
        //        WarehouseBLL warehouseBLL = null;
        //        var stockBLL = new ProductStockBLL(user, factory);
        //        var promotionBLL = new StockPromotionMainBLL(user, factory);
        //        var list = new List<ProductPriceStock>();

        //        foreach (var product_id in product_ids)
        //        {
        //            //提取促销价
        //            var price = promotionBLL.GetPrice(department_id, product_id, price_type);

        //            //提取部门价
        //            if (price == 0 && ComJudge.ProductIsOwnDepartmentPrice(bookConfig.erp_type))
        //            {
        //                if (priceBLL == null)
        //                {
        //                    priceBLL = new ProductPriceBLL(user, factory);
        //                }
        //                price = priceBLL.GetPrice(department_id, product_id, price_type);
        //            }

        //            //提取商品价
        //            if (price == 0)
        //            {
        //                price = GetPrice(product_id, price_type);
        //                if (price == 0 && price_type != "retail_price")
        //                {
        //                    price = GetPrice(product_id, "retail_price");  //提取零售价
        //                }
        //            }

        //            //提取库存信息
        //            List<ProductStock> stocks = null;

        //            switch (bookConfig.erp_type)
        //            {
        //                case ERPType.BUSINESS:
        //                    stocks = stockBLL.GetListForChoice(department_id, null, product_id);
        //                    break;
        //                case ERPType.FOOD:
        //                case ERPType.INVENTORY:
        //                case ERPType.INSTRUMENT:
        //                case ERPType.DRUG:
        //                    stocks = stockBLL.GetListForBatchNoChoice(department_id, null, product_id, null);
        //                    break;
        //            }

        //            if (stocks == null)
        //            {
        //                stocks = new List<ProductStock>();
        //            }

        //            //给商品添加默认仓库
        //            if (stocks.Count == 0 && paramConfig.is_negative_stock == 1 && ComRequire.IsRequireWarehouseProduct(bookConfig.erp_type))
        //            {
        //                if (warehouseBLL == null)
        //                {
        //                    warehouseBLL = new WarehouseBLL(user, factory);
        //                }
        //                var ws = warehouseBLL.GetFirst(department_id);
        //                if (ws != null)
        //                {
        //                    stocks.Add(new ProductStock() { warehouse_id = ws.id, warehouse_name = ws.name });
        //                }
        //            }

        //            list.Add(new ProductPriceStock()
        //            {
        //                id = product_id,
        //                price = price,
        //                stocks = stocks

        //            });
        //        }

        //        return list;
        //    }
        //    finally
        //    {
        //        if (isCreator) base.CloseSqlHelper();
        //    }
        //}


        public async Task<(JObject?, Exception?)> GetPageJArrayForWwz(int pageIndex, int pageSize)
        {
            try
            {
                var jo = await dal.GetPageJArrayForWwzAsync(pageIndex, pageSize);
                return (jo, null);
            }
            catch (Exception ex)
            {
                return (null, ex);
            }
        }

    }
}
